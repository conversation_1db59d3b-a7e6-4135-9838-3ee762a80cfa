-- NIRA Database Schema Updates for Month 1 Features
-- Implements FSRS (Free Spaced Repetition Scheduler) and Micro-Assessment Framework
-- Run these migrations in Supabase Dashboard or via CLI

-- ============================================================================
-- 1. FSRS (Spaced Repetition System) Tables
-- ============================================================================

-- FSRS Cards: Stores spaced repetition data for each learning item
CREATE TABLE IF NOT EXISTS fsrs_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    learning_item_id UUID NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- FSRS Algorithm Fields
    due TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    stability DECIMAL(10,4) NOT NULL DEFAULT 2.5,
    difficulty DECIMAL(10,4) NOT NULL DEFAULT 5.0,
    elapsed_days INTEGER NOT NULL DEFAULT 0,
    scheduled_days INTEGER NOT NULL DEFAULT 0,
    reps INTEGER NOT NULL DEFAULT 0,
    lapses INTEGER NOT NULL DEFAULT 0,
    
    -- Card State Management
    state VARCHAR(20) NOT NULL DEFAULT 'new' CHECK (state IN ('new', 'learning', 'review', 'relearning')),
    last_review TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    retrievability DECIMAL(6,4) NOT NULL DEFAULT 1.0,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, learning_item_id),
    CHECK (stability > 0),
    CHECK (difficulty >= 1 AND difficulty <= 10),
    CHECK (retrievability >= 0 AND retrievability <= 1)
);

-- FSRS Review Logs: Detailed history of all review sessions
CREATE TABLE IF NOT EXISTS fsrs_review_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    card_id UUID NOT NULL REFERENCES fsrs_cards(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Review Details
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 4), -- 1=Again, 2=Hard, 3=Good, 4=Easy
    state VARCHAR(20) NOT NULL,
    due TIMESTAMPTZ NOT NULL,
    
    -- FSRS Metrics at time of review
    stability DECIMAL(10,4) NOT NULL,
    difficulty DECIMAL(10,4) NOT NULL,
    elapsed_days INTEGER NOT NULL,
    last_elapsed_days INTEGER NOT NULL,
    scheduled_days INTEGER NOT NULL,
    
    -- Timing
    review_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    response_time_ms INTEGER, -- Time taken to respond
    
    -- Context
    review_context JSONB DEFAULT '{}', -- Device, time of day, etc.
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Review Schedule: Active review queue for users
CREATE TABLE IF NOT EXISTS review_schedule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    learning_item_id UUID NOT NULL,
    card_id UUID NOT NULL REFERENCES fsrs_cards(id) ON DELETE CASCADE,
    
    -- Schedule Details
    next_review_date TIMESTAMPTZ NOT NULL,
    difficulty DECIMAL(10,4) NOT NULL,
    stability DECIMAL(10,4) NOT NULL,
    retrievability DECIMAL(6,4) NOT NULL,
    
    -- Priority and Organization
    priority_score DECIMAL(8,4) DEFAULT 0, -- For sorting review order
    is_overdue BOOLEAN GENERATED ALWAYS AS (next_review_date < NOW()) STORED,
    days_overdue INTEGER GENERATED ALWAYS AS (
        CASE WHEN next_review_date < NOW() 
        THEN EXTRACT(DAY FROM NOW() - next_review_date)::INTEGER 
        ELSE 0 END
    ) STORED,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 2. Micro-Assessment Framework Tables
-- ============================================================================

-- Micro Assessments: Generated assessments every 3-5 learning units
CREATE TABLE IF NOT EXISTS micro_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Assessment Configuration
    target_skills TEXT[] NOT NULL, -- Array of skill categories
    difficulty_level INTEGER NOT NULL DEFAULT 5 CHECK (difficulty_level >= 1 AND difficulty_level <= 10),
    estimated_duration INTEGER NOT NULL, -- in seconds
    
    -- Adaptive Parameters
    adaptive_params JSONB NOT NULL DEFAULT '{
        "targetAccuracy": 0.8,
        "maxDifficultyIncrease": 2,
        "confidenceThreshold": 0.7,
        "adaptationRate": 0.1
    }',
    
    -- Status Tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed', 'expired')),
    trigger_reason VARCHAR(50) NOT NULL DEFAULT 'unit_completion',
    
    -- Timing
    created_at TIMESTAMPTZ DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '24 hours'
);

-- Assessment Items: Individual questions/tasks within assessments
CREATE TABLE IF NOT EXISTS assessment_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assessment_id UUID NOT NULL REFERENCES micro_assessments(id) ON DELETE CASCADE,
    
    -- Item Configuration
    type VARCHAR(20) NOT NULL CHECK (type IN ('multiple_choice', 'fill_in_blank', 'speaking', 'writing')),
    skill VARCHAR(20) NOT NULL CHECK (skill IN ('vocabulary', 'grammar', 'listening', 'speaking', 'reading', 'writing', 'cultural')),
    
    -- Content
    question TEXT NOT NULL,
    correct_answer TEXT NOT NULL,
    options JSONB DEFAULT '[]', -- Array of possible answers for MC questions
    
    -- Media
    audio_url TEXT,
    image_url TEXT,
    
    -- Difficulty and Timing
    difficulty_level INTEGER NOT NULL DEFAULT 5,
    time_limit INTEGER NOT NULL DEFAULT 30, -- seconds
    
    -- Metadata for analytics
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Assessment Responses: User answers to assessment items
CREATE TABLE IF NOT EXISTS assessment_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assessment_id UUID NOT NULL REFERENCES micro_assessments(id) ON DELETE CASCADE,
    item_id UUID NOT NULL REFERENCES assessment_items(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Response Data
    user_answer TEXT NOT NULL,
    correct_answer TEXT NOT NULL,
    is_correct BOOLEAN NOT NULL,
    
    -- Performance Metrics
    time_spent INTEGER NOT NULL, -- milliseconds
    attempts INTEGER NOT NULL DEFAULT 1,
    confidence_level DECIMAL(3,2), -- 0.00 to 1.00, if provided by user
    
    -- Analysis
    response_analysis JSONB DEFAULT '{}', -- Detailed analysis of the response
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(assessment_id, item_id, user_id)
);

-- ============================================================================
-- 3. Learning Analytics and Progress Tracking
-- ============================================================================

-- Learning Unit Completions: Track every learning unit completion
CREATE TABLE IF NOT EXISTS learning_unit_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    lesson_id UUID,
    unit_id TEXT NOT NULL, -- Could be vocabulary_item_id, grammar_rule_id, etc.
    unit_type VARCHAR(20) NOT NULL CHECK (unit_type IN ('vocabulary', 'grammar', 'conversation', 'phrase', 'exercise')),
    
    -- Performance Metrics
    accuracy_score DECIMAL(5,4) NOT NULL CHECK (accuracy_score >= 0 AND accuracy_score <= 1),
    completion_time INTEGER NOT NULL, -- seconds
    attempts_needed INTEGER NOT NULL DEFAULT 1,
    difficulty_level INTEGER NOT NULL,
    engagement_score DECIMAL(5,4) DEFAULT 0.5, -- 0-1 scale
    
    -- Context
    completion_context JSONB DEFAULT '{}', -- Time of day, device, biometric data, etc.
    
    completed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Skill Progression: Track long-term skill development
CREATE TABLE IF NOT EXISTS skill_progression (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    skill VARCHAR(20) NOT NULL CHECK (skill IN ('vocabulary', 'grammar', 'listening', 'speaking', 'reading', 'writing', 'cultural')),
    
    -- Progression Metrics
    current_level INTEGER NOT NULL DEFAULT 1,
    experience_points DECIMAL(10,2) NOT NULL DEFAULT 0,
    mastery_percentage DECIMAL(5,2) NOT NULL DEFAULT 0 CHECK (mastery_percentage >= 0 AND mastery_percentage <= 100),
    
    -- Recent Performance (last 10 assessments)
    recent_scores DECIMAL(5,4)[] DEFAULT '{}',
    performance_trend VARCHAR(20) DEFAULT 'stable' CHECK (performance_trend IN ('improving', 'stable', 'declining')),
    
    -- Timestamps
    last_assessed TIMESTAMPTZ,
    last_practiced TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, skill)
);

-- Assessment Reports: Comprehensive analysis of assessment performance
CREATE TABLE IF NOT EXISTS assessment_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assessment_id UUID NOT NULL REFERENCES micro_assessments(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Overall Performance
    overall_score DECIMAL(5,4) NOT NULL,
    total_time_spent INTEGER NOT NULL, -- seconds
    completion_percentage DECIMAL(5,2) NOT NULL,
    
    -- Skill Breakdown
    skill_scores JSONB NOT NULL DEFAULT '{}', -- {"vocabulary": 0.85, "grammar": 0.72, ...}
    
    -- Analysis and Recommendations
    performance_analysis JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '[]', -- Array of recommendation objects
    improved_areas TEXT[] DEFAULT '{}',
    areas_for_focus TEXT[] DEFAULT '{}',
    
    -- Comparison with Previous Assessments
    performance_change JSONB DEFAULT '{}', -- Comparison with previous similar assessments
    
    generated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 4. Enhanced Analytics Tables
-- ============================================================================

-- Learning Sessions: Comprehensive session tracking
CREATE TABLE IF NOT EXISTS learning_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Session Details
    session_type VARCHAR(30) NOT NULL DEFAULT 'regular' CHECK (session_type IN ('regular', 'review', 'assessment', 'intensive')),
    lesson_ids UUID[] DEFAULT '{}',
    units_completed INTEGER NOT NULL DEFAULT 0,
    
    -- Performance
    overall_accuracy DECIMAL(5,4),
    average_response_time INTEGER, -- milliseconds
    engagement_score DECIMAL(5,4),
    
    -- Biometric Integration
    initial_stress_level DECIMAL(5,4),
    final_stress_level DECIMAL(5,4),
    avg_heart_rate INTEGER,
    cognitive_load_events INTEGER DEFAULT 0, -- Number of times cognitive overload was detected
    
    -- Context
    device_type VARCHAR(20),
    time_of_day VARCHAR(10), -- morning, afternoon, evening, night
    session_context JSONB DEFAULT '{}',
    
    -- Timing
    started_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    duration INTEGER GENERATED ALWAYS AS (
        CASE WHEN ended_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM ended_at - started_at)::INTEGER 
        ELSE NULL END
    ) STORED
);

-- Learning Recommendations: AI-generated personalized recommendations
CREATE TABLE IF NOT EXISTS learning_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Recommendation Details
    type VARCHAR(20) NOT NULL CHECK (type IN ('review', 'practice', 'advance', 'remediate', 'break')),
    skill VARCHAR(20) CHECK (skill IN ('vocabulary', 'grammar', 'listening', 'speaking', 'reading', 'writing', 'cultural')),
    priority VARCHAR(10) NOT NULL CHECK (priority IN ('high', 'medium', 'low')),
    
    -- Content
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    suggested_actions JSONB DEFAULT '[]', -- Array of specific actions
    
    -- Targeting
    target_difficulty INTEGER,
    estimated_duration INTEGER, -- minutes
    
    -- AI Generation Context
    generated_from VARCHAR(50) NOT NULL, -- 'assessment_result', 'session_analysis', 'fsrs_analysis'
    confidence_score DECIMAL(5,4) NOT NULL, -- AI confidence in recommendation
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'accepted', 'dismissed', 'expired')),
    
    -- Timing
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '7 days',
    acted_on_at TIMESTAMPTZ
);

-- ============================================================================
-- 5. Indexes for Performance Optimization
-- ============================================================================

-- FSRS Indexes
CREATE INDEX IF NOT EXISTS idx_fsrs_cards_user_due ON fsrs_cards(user_id, due);
CREATE INDEX IF NOT EXISTS idx_fsrs_cards_learning_item ON fsrs_cards(learning_item_id);
CREATE INDEX IF NOT EXISTS idx_fsrs_review_logs_card_time ON fsrs_review_logs(card_id, review_time);
CREATE INDEX IF NOT EXISTS idx_review_schedule_user_due ON review_schedule(user_id, next_review_date);
CREATE INDEX IF NOT EXISTS idx_review_schedule_overdue ON review_schedule(user_id) WHERE is_overdue = true;

-- Assessment Indexes
CREATE INDEX IF NOT EXISTS idx_micro_assessments_user_status ON micro_assessments(user_id, status);
CREATE INDEX IF NOT EXISTS idx_assessment_responses_assessment ON assessment_responses(assessment_id);
CREATE INDEX IF NOT EXISTS idx_assessment_responses_user ON assessment_responses(user_id, created_at);

-- Analytics Indexes
CREATE INDEX IF NOT EXISTS idx_learning_units_user_time ON learning_unit_completions(user_id, completed_at);
CREATE INDEX IF NOT EXISTS idx_skill_progression_user ON skill_progression(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_sessions_user_time ON learning_sessions(user_id, started_at);
CREATE INDEX IF NOT EXISTS idx_recommendations_user_active ON learning_recommendations(user_id) WHERE status = 'active';

-- Compound indexes for common queries
CREATE INDEX IF NOT EXISTS idx_fsrs_cards_user_state_due ON fsrs_cards(user_id, state, due);
CREATE INDEX IF NOT EXISTS idx_learning_units_user_type_time ON learning_unit_completions(user_id, unit_type, completed_at);

-- ============================================================================
-- 6. Row Level Security (RLS) Policies
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE fsrs_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE fsrs_review_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE micro_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_unit_completions ENABLE ROW LEVEL SECURITY;
ALTER TABLE skill_progression ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_recommendations ENABLE ROW LEVEL SECURITY;

-- User-specific access policies
CREATE POLICY "Users can access their own FSRS cards" ON fsrs_cards
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own review logs" ON fsrs_review_logs
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own review schedule" ON review_schedule
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own assessments" ON micro_assessments
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access items from their assessments" ON assessment_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM micro_assessments 
            WHERE id = assessment_items.assessment_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can access their own assessment responses" ON assessment_responses
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own learning unit completions" ON learning_unit_completions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own skill progression" ON skill_progression
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own assessment reports" ON assessment_reports
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own learning sessions" ON learning_sessions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access their own recommendations" ON learning_recommendations
    FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- 7. Triggers for Automatic Updates
-- ============================================================================

-- Update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to relevant tables
CREATE TRIGGER update_fsrs_cards_updated_at BEFORE UPDATE ON fsrs_cards
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_review_schedule_updated_at BEFORE UPDATE ON review_schedule
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_skill_progression_updated_at BEFORE UPDATE ON skill_progression
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 8. Views for Common Queries
-- ============================================================================

-- Due Reviews View: Easy access to reviews that need attention
CREATE OR REPLACE VIEW due_reviews AS
SELECT 
    rs.*,
    fc.reps,
    fc.lapses,
    fc.state,
    CASE 
        WHEN rs.next_review_date < NOW() - INTERVAL '1 day' THEN 'critical'
        WHEN rs.next_review_date < NOW() THEN 'overdue'
        WHEN rs.next_review_date < NOW() + INTERVAL '1 hour' THEN 'due_soon'
        ELSE 'scheduled'
    END as urgency_level
FROM review_schedule rs
JOIN fsrs_cards fc ON rs.card_id = fc.id
WHERE rs.next_review_date <= NOW() + INTERVAL '1 hour'
ORDER BY rs.next_review_date ASC;

-- User Performance Summary View
CREATE OR REPLACE VIEW user_performance_summary AS
SELECT 
    user_id,
    COUNT(*) as total_reviews,
    AVG(CASE WHEN frl.rating >= 3 THEN 1.0 ELSE 0.0 END) as success_rate,
    AVG(frl.response_time_ms) as avg_response_time,
    COUNT(*) FILTER (WHERE frl.review_time >= NOW() - INTERVAL '7 days') as reviews_this_week,
    COUNT(*) FILTER (WHERE frl.review_time >= NOW() - INTERVAL '30 days') as reviews_this_month
FROM fsrs_review_logs frl
GROUP BY user_id;

-- Assessment Performance View
CREATE OR REPLACE VIEW assessment_performance AS
SELECT 
    ar.user_id,
    ar.assessment_id,
    ma.target_skills,
    ar.overall_score,
    ar.completion_percentage,
    COUNT(are.id) as total_items,
    COUNT(are.id) FILTER (WHERE are.is_correct = true) as correct_items,
    AVG(are.time_spent) as avg_time_per_item,
    ar.generated_at
FROM assessment_reports ar
JOIN micro_assessments ma ON ar.assessment_id = ma.id
LEFT JOIN assessment_responses are ON ar.assessment_id = are.assessment_id
GROUP BY ar.user_id, ar.assessment_id, ma.target_skills, ar.overall_score, 
         ar.completion_percentage, ar.generated_at;

-- ============================================================================
-- 9. Sample Data and Initial Configuration
-- ============================================================================

-- Insert default FSRS parameters (can be customized per user later)
INSERT INTO public.app_settings (key, value, description) VALUES 
('fsrs_default_weights', '[0.4072, 1.1829, 3.1262, 15.4722, 7.2102, 0.5316, 1.0651, 0.0234, 1.616, 0.1544, 1.0824, 1.9813, 0.0953, 0.2975, 2.2042, 0.2407, 2.9466, 0.5034, 0.6567]', 'Default FSRS algorithm weights'),
('micro_assessment_interval', '4', 'Number of learning units before triggering micro-assessment'),
('assessment_expiry_hours', '24', 'Hours before an assessment expires'),
('max_daily_reviews', '100', 'Maximum number of reviews per day per user')
ON CONFLICT (key) DO NOTHING;

-- ============================================================================
-- 10. Analytics Functions
-- ============================================================================

-- Function to get user's current learning streak
CREATE OR REPLACE FUNCTION get_learning_streak(p_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    streak_count INTEGER := 0;
    check_date DATE := CURRENT_DATE;
BEGIN
    LOOP
        -- Check if user completed any learning units on this date
        IF EXISTS (
            SELECT 1 FROM learning_unit_completions 
            WHERE user_id = p_user_id 
            AND DATE(completed_at) = check_date
        ) THEN
            streak_count := streak_count + 1;
            check_date := check_date - INTERVAL '1 day';
        ELSE
            EXIT;
        END IF;
    END LOOP;
    
    RETURN streak_count;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate skill mastery score
CREATE OR REPLACE FUNCTION calculate_skill_mastery(p_user_id UUID, p_skill TEXT)
RETURNS DECIMAL(5,2) AS $$
DECLARE
    recent_scores DECIMAL(5,4)[];
    mastery_score DECIMAL(5,2);
BEGIN
    -- Get recent assessment scores for this skill
    SELECT ARRAY_AGG(
        CASE WHEN are.is_correct THEN 1.0 ELSE 0.0 END
        ORDER BY are.created_at DESC
    ) INTO recent_scores
    FROM assessment_responses are
    JOIN assessment_items ai ON are.item_id = ai.id
    WHERE are.user_id = p_user_id 
    AND ai.skill = p_skill
    AND are.created_at >= NOW() - INTERVAL '30 days'
    LIMIT 20;
    
    -- Calculate weighted mastery (recent performance weighted more heavily)
    IF array_length(recent_scores, 1) > 0 THEN
        SELECT 
            (SUM(score * weight) / SUM(weight)) * 100
        INTO mastery_score
        FROM (
            SELECT 
                unnest(recent_scores) as score,
                generate_series(array_length(recent_scores, 1), 1, -1) as weight
        ) weighted_scores;
    ELSE
        mastery_score := 0;
    END IF;
    
    RETURN COALESCE(mastery_score, 0);
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- End of Schema Updates
-- ============================================================================

-- Summary of changes:
-- 1. Added FSRS tables for intelligent spaced repetition
-- 2. Added micro-assessment framework tables
-- 3. Enhanced analytics and progress tracking
-- 4. Added performance indexes and RLS policies
-- 5. Created useful views and helper functions
-- 6. Implemented proper data relationships and constraints

COMMENT ON SCHEMA public IS 'NIRA Language Learning Platform - Enhanced with FSRS and Micro-Assessment Framework'; 