//
//  EnhancedLearningAgent.swift
//  NIRA
//
//  Enhanced Learning Agent Model with Modern Capabilities
//  Based on Language Learning Content Development Standards v2.0
//

import Foundation
import SwiftUI

struct EnhancedLearningAgent: Identifiable, Codable {
    let id: UUID
    let name: String
    let persona: Agent<PERSON>ersona
    let language: Language
    let avatarEmoji: String?
    let specializations: [String]
    let culturalExpertise: [String]
    let expertiseLevel: Double // 1.0 - 10.0
    let adaptiveLearningCapability: Double // 0.0 - 1.0
    let culturalSpecialization: CulturalSpecialization
    let biometricIntegration: Bool
    let socialLearningFeatures: [SocialLearningFeature]
    let voicePracticeAvailable: Bool
    let isAvailable: Bool
    let responseTime: TimeInterval
    let successRate: Double
    let learningPathsSupported: [AgentLearningPath]
    
    // Real-time adaptive features
    let cognitiveLoadOptimization: Bool
    let microLearningSupport: Bool
    let culturalContextIntegration: Bool
    let biometricResponseCapability: Bool
    
    // Performance metrics
    let userSatisfactionScore: Double
    let learningEffectivenessScore: Double
    let culturalAccuracyScore: Double
    let adaptabilityScore: Double
    
    static func getEnhancedAgents(for language: Language) -> [EnhancedLearningAgent] {
        switch language {
        case .tamil:
            return tamilAgents
        case .spanish:
            return spanishAgents
        case .french:
            return frenchAgents
        case .german:
            return germanAgents
        case .japanese:
            return japaneseAgents
        case .chinese:
            return mandarinAgents
        case .hindi:
            return hindiAgents
        case .arabic:
            return arabicAgents
        case .portuguese:
            return portugueseAgents
        case .russian:
            return russianAgents
        default:
            return []
        }
    }
    
    // MARK: - Tamil Agents (Enhanced from existing)
    
    private static let tamilAgents: [EnhancedLearningAgent] = [
        EnhancedLearningAgent(
            id: UUID(),
            name: "Priya - Cultural Navigator",
            persona: .culturalSeeker,
            language: .tamil,
            avatarEmoji: "👩🏽‍🏫",
            specializations: ["Cultural Context", "Traditional Expressions", "Family Relationships", "Festival Celebrations"],
            culturalExpertise: ["Tamil Literature", "Classical Music", "Temple Traditions", "Food Culture", "Wedding Customs"],
            expertiseLevel: 9.5,
            adaptiveLearningCapability: 0.92,
            culturalSpecialization: CulturalSpecialization(
                score: 9.8,
                authenticityLevel: .native,
                contextualAccuracy: 0.95,
                regionalVariations: ["Chennai", "Madurai", "Coimbatore", "Trichy"]
            ),
            biometricIntegration: true,
            socialLearningFeatures: [
                .culturalExchangeForums,
                .traditionShareCircles,
                .festivalCelebrationGroups,
                .languageExchangePartners
            ],
            voicePracticeAvailable: true,
            isAvailable: true,
            responseTime: 0.8,
            successRate: 0.94,
            learningPathsSupported: [.cultural, .traditional, .family, .business],
            cognitiveLoadOptimization: true,
            microLearningSupport: true,
            culturalContextIntegration: true,
            biometricResponseCapability: true,
            userSatisfactionScore: 9.3,
            learningEffectivenessScore: 9.1,
            culturalAccuracyScore: 9.8,
            adaptabilityScore: 9.4
        ),
        
        EnhancedLearningAgent(
            id: UUID(),
            name: "Ravi - Grammar Master",
            persona: .busyProfessional,
            language: .tamil,
            avatarEmoji: "👨🏽‍🎓",
            specializations: ["Grammar Rules", "Sentence Structure", "Verb Conjugations", "Advanced Syntax"],
            culturalExpertise: ["Classical Tamil", "Modern Usage", "Literary Forms", "Academic Writing"],
            expertiseLevel: 9.8,
            adaptiveLearningCapability: 0.89,
            culturalSpecialization: CulturalSpecialization(
                score: 8.5,
                authenticityLevel: .academic,
                contextualAccuracy: 0.98,
                regionalVariations: ["Standard Tamil", "Literary Tamil"]
            ),
            biometricIntegration: true,
            socialLearningFeatures: [
                .studyGroups,
                .grammarChallenges,
                .peerReview,
                .academicDiscussions
            ],
            voicePracticeAvailable: true,
            isAvailable: true,
            responseTime: 0.6,
            successRate: 0.97,
            learningPathsSupported: [.academic, .professional, .literary],
            cognitiveLoadOptimization: true,
            microLearningSupport: true,
            culturalContextIntegration: false,
            biometricResponseCapability: true,
            userSatisfactionScore: 9.0,
            learningEffectivenessScore: 9.6,
            culturalAccuracyScore: 8.5,
            adaptabilityScore: 8.9
        ),
        
        EnhancedLearningAgent(
            id: UUID(),
            name: "Meera - Conversation Coach",
            persona: .socialLearner,
            language: .tamil,
            avatarEmoji: "👩🏽‍💼",
            specializations: ["Daily Conversations", "Pronunciation", "Listening Skills", "Real-world Scenarios"],
            culturalExpertise: ["Business Etiquette", "Social Interactions", "Phone Conversations", "Market Shopping"],
            expertiseLevel: 9.2,
            adaptiveLearningCapability: 0.95,
            culturalSpecialization: CulturalSpecialization(
                score: 9.0,
                authenticityLevel: .native,
                contextualAccuracy: 0.92,
                regionalVariations: ["Urban Tamil", "Professional Tamil"]
            ),
            biometricIntegration: true,
            socialLearningFeatures: [
                .conversationCircles,
                .rolePlayScenarios,
                .pronunciationChallenges,
                .realWorldPractice
            ],
            voicePracticeAvailable: true,
            isAvailable: true,
            responseTime: 0.5,
            successRate: 0.91,
            learningPathsSupported: [.conversational, .business, .travel, .social],
            cognitiveLoadOptimization: true,
            microLearningSupport: true,
            culturalContextIntegration: true,
            biometricResponseCapability: true,
            userSatisfactionScore: 9.4,
            learningEffectivenessScore: 9.2,
            culturalAccuracyScore: 9.0,
            adaptabilityScore: 9.5
        )
    ]
    
    // MARK: - Spanish Agents
    
    private static let spanishAgents: [EnhancedLearningAgent] = [
        EnhancedLearningAgent(
            id: UUID(),
            name: "Carlos - Latin Culture Expert",
            persona: .culturalSeeker,
            language: .spanish,
            avatarEmoji: "👨🏽‍🎨",
            specializations: ["Latin American Culture", "Regional Dialects", "Music & Arts", "Historical Context"],
            culturalExpertise: ["Mexican Traditions", "Argentine Tango", "Colombian Coffee Culture", "Spanish Literature"],
            expertiseLevel: 9.3,
            adaptiveLearningCapability: 0.88,
            culturalSpecialization: CulturalSpecialization(
                score: 9.5,
                authenticityLevel: .native,
                contextualAccuracy: 0.93,
                regionalVariations: ["Mexico", "Argentina", "Colombia", "Spain"]
            ),
            biometricIntegration: true,
            socialLearningFeatures: [
                .culturalExchangeForums,
                .musicAppreciationGroups,
                .cookingClasses,
                .travelPlanning
            ],
            voicePracticeAvailable: true,
            isAvailable: true,
            responseTime: 0.7,
            successRate: 0.92,
            learningPathsSupported: [.cultural, .travel, .arts, .history],
            cognitiveLoadOptimization: true,
            microLearningSupport: true,
            culturalContextIntegration: true,
            biometricResponseCapability: true,
            userSatisfactionScore: 9.2,
            learningEffectivenessScore: 8.9,
            culturalAccuracyScore: 9.5,
            adaptabilityScore: 9.0
        )
    ]
    
    // MARK: - Sample Agent for Previews
    
    static let sampleAgent = EnhancedLearningAgent(
        id: UUID(),
        name: "Priya - Cultural Navigator",
        persona: .culturalSeeker,
        language: .tamil,
        avatarEmoji: "👩🏽‍🏫",
        specializations: ["Cultural Context", "Traditional Expressions", "Family Relationships", "Festival Celebrations"],
        culturalExpertise: ["Tamil Literature", "Classical Music", "Temple Traditions", "Food Culture", "Wedding Customs"],
        expertiseLevel: 9.5,
        adaptiveLearningCapability: 0.92,
        culturalSpecialization: CulturalSpecialization(
            score: 9.8,
            authenticityLevel: .native,
            contextualAccuracy: 0.95,
            regionalVariations: ["Chennai", "Madurai", "Coimbatore", "Trichy"]
        ),
        biometricIntegration: true,
        socialLearningFeatures: [
            .culturalExchangeForums,
            .traditionShareCircles,
            .festivalCelebrationGroups,
            .languageExchangePartners
        ],
        voicePracticeAvailable: true,
        isAvailable: true,
        responseTime: 0.8,
        successRate: 0.94,
        learningPathsSupported: [.cultural, .traditional, .family, .business],
        cognitiveLoadOptimization: true,
        microLearningSupport: true,
        culturalContextIntegration: true,
        biometricResponseCapability: true,
        userSatisfactionScore: 9.3,
        learningEffectivenessScore: 9.1,
        culturalAccuracyScore: 9.8,
        adaptabilityScore: 9.4
    )
    
    // Add more language-specific agents as needed...
    private static let frenchAgents: [EnhancedLearningAgent] = []
    private static let germanAgents: [EnhancedLearningAgent] = []
    private static let japaneseAgents: [EnhancedLearningAgent] = []
    private static let mandarinAgents: [EnhancedLearningAgent] = []
    private static let hindiAgents: [EnhancedLearningAgent] = []
    private static let arabicAgents: [EnhancedLearningAgent] = []
    private static let portugueseAgents: [EnhancedLearningAgent] = []
    private static let russianAgents: [EnhancedLearningAgent] = []
}

// MARK: - Supporting Models

struct CulturalSpecialization: Codable {
    let score: Double // 1.0 - 10.0
    let authenticityLevel: AuthenticityLevel
    let contextualAccuracy: Double // 0.0 - 1.0
    let regionalVariations: [String]
}

enum AuthenticityLevel: String, Codable {
    case native = "Native"
    case fluent = "Fluent"
    case academic = "Academic"
    case learning = "Learning"
}

enum SocialLearningFeature: String, Codable, CaseIterable {
    case culturalExchangeForums = "Cultural Exchange Forums"
    case traditionShareCircles = "Tradition Share Circles"
    case festivalCelebrationGroups = "Festival Celebration Groups"
    case languageExchangePartners = "Language Exchange Partners"
    case studyGroups = "Study Groups"
    case grammarChallenges = "Grammar Challenges"
    case peerReview = "Peer Review"
    case academicDiscussions = "Academic Discussions"
    case conversationCircles = "Conversation Circles"
    case rolePlayScenarios = "Role Play Scenarios"
    case pronunciationChallenges = "Pronunciation Challenges"
    case realWorldPractice = "Real World Practice"
    case musicAppreciationGroups = "Music Appreciation Groups"
    case cookingClasses = "Cooking Classes"
    case travelPlanning = "Travel Planning"
}

enum AgentLearningPath: String, Codable, CaseIterable {
    case cultural = "Cultural"
    case traditional = "Traditional"
    case family = "Family"
    case business = "Business"
    case academic = "Academic"
    case professional = "Professional"
    case literary = "Literary"
    case conversational = "Conversational"
    case travel = "Travel"
    case social = "Social"
    case arts = "Arts"
    case history = "History"
}

// MARK: - Extensions for UI

extension EnhancedLearningAgent {
    var culturalExpertiseScore: Double {
        return culturalSpecialization.score
    }
    
    var overallScore: Double {
        return (expertiseLevel + 
                adaptiveLearningCapability * 10 + 
                culturalSpecialization.score + 
                userSatisfactionScore + 
                learningEffectivenessScore) / 5.0
    }
    
    var isHighlyAdaptive: Bool {
        return adaptiveLearningCapability > 0.85
    }
    
    var isCulturalExpert: Bool {
        return culturalSpecialization.score > 8.0
    }
    
    var hasSocialFeatures: Bool {
        return socialLearningFeatures.count > 2
    }
} 