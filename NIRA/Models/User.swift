import Foundation
import SwiftData

@Model
final class User {
    var id: UUID
    var email: String
    var firstName: String
    var lastName: String
    var auth0ID: String?
    var preferredLanguages: [Language]
    var currentLevelRawValue: Int?
    var targetLanguages: [Language]
    var currentStreak: Int
    var longestStreak: Int
    var totalLessonsCompleted: Int
    var totalPointsEarned: Int
    var joinDate: Date = Date()
    var lastActiveDate: Date = Date()
    var profileImageURL: String?
    var achievements: [Achievement] = []
    var createdAt: Date = Date()

    init(
        id: UUID = UUID(),
        email: String = "",
        firstName: String = "",
        lastName: String = "",
        auth0ID: String? = nil,
        preferredLanguages: [Language] = [.english],
        currentLevel: SkillLevel? = nil,
        targetLanguages: [Language] = [],
        currentStreak: Int = 0,
        longestStreak: Int = 0,
        totalLessonsCompleted: Int = 0,
        totalPointsEarned: Int = 0,
        joinDate: Date = Date(),
        lastActiveDate: Date = Date(),
        profileImageURL: String? = nil,
        achievements: [Achievement] = [],
        createdAt: Date = Date()
    ) {
        self.id = id
        self.email = email
        self.firstName = firstName
        self.lastName = lastName
        self.auth0ID = auth0ID
        self.preferredLanguages = preferredLanguages
        self.currentLevelRawValue = currentLevel?.rawValue
        self.targetLanguages = targetLanguages
        self.currentStreak = currentStreak
        self.longestStreak = longestStreak
        self.totalLessonsCompleted = totalLessonsCompleted
        self.totalPointsEarned = totalPointsEarned
        self.joinDate = joinDate
        self.lastActiveDate = lastActiveDate
        self.profileImageURL = profileImageURL
        self.achievements = achievements
        self.createdAt = createdAt
    }

    var currentLevel: SkillLevel? {
        get {
            guard let rawValue = currentLevelRawValue else { return nil }
            return SkillLevel(rawValue: rawValue)
        }
        set {
            currentLevelRawValue = newValue?.rawValue
        }
    }

    var fullName: String {
        return "\(firstName) \(lastName)"
    }

    var isActive: Bool {
        let daysSinceLastActive = Calendar.current.dateComponents([.day], from: lastActiveDate, to: Date()).day ?? 0
        return daysSinceLastActive < 2
    }
}

// MARK: - Supporting Models

// SkillLevel is defined in CurriculumService.swift

enum Language: String, CaseIterable, Codable {
    case french = "french"
    case english = "english"
    case spanish = "spanish"
    case japanese = "japanese"
    case tamil = "tamil"
    case korean = "korean"
    case italian = "italian"
    case german = "german"
    case hindi = "hindi"
    case chinese = "chinese"
    case portuguese = "portuguese"
    case telugu = "telugu"
    case vietnamese = "vietnamese"
    case indonesian = "indonesian"
    case arabic = "arabic"
    // Previous 10 languages
    case kannada = "kannada"
    case malayalam = "malayalam"
    case bengali = "bengali"
    case marathi = "marathi"
    case punjabi = "punjabi"
    case dutch = "dutch"
    case swedish = "swedish"
    case thai = "thai"
    case russian = "russian"
    case norwegian = "norwegian"
    // Additional 25 languages to reach 50
    case gujarati = "gujarati"
    case odia = "odia"
    case assamese = "assamese"
    case konkani = "konkani"
    case sindhi = "sindhi"
    case bhojpuri = "bhojpuri"
    case maithili = "maithili"
    case swahili = "swahili"
    case hebrew = "hebrew"
    case greek = "greek"
    case turkish = "turkish"
    case farsi = "farsi"
    case tagalog = "tagalog"
    case ukrainian = "ukrainian"
    case danish = "danish"
    case xhosa = "xhosa"
    case zulu = "zulu"
    case amharic = "amharic"
    case quechua = "quechua"
    case maori = "maori"
    case cherokee = "cherokee"
    case navajo = "navajo"
    case hawaiian = "hawaiian"
    case inuktitut = "inuktitut"
    case yoruba = "yoruba"
    // Additional languages to complete the 50-language expansion
    case urdu = "urdu"
    case polish = "polish"
    case czech = "czech"
    case hungarian = "hungarian"
    case romanian = "romanian"
    case bulgarian = "bulgarian"
    case croatian = "croatian"
    case serbian = "serbian"
    case slovak = "slovak"
    case slovenian = "slovenian"
    case estonian = "estonian"
    case latvian = "latvian"
    case lithuanian = "lithuanian"
    case maltese = "maltese"
    case irish = "irish"
    case welsh = "welsh"
    case scots = "scots"
    case manx = "manx"
    case cornish = "cornish"
    case breton = "breton"
    case basque = "basque"
    case catalan = "catalan"
    case galician = "galician"

    var displayName: String {
        switch self {
        case .french: return "French"
        case .english: return "English"
        case .spanish: return "Spanish"
        case .japanese: return "Japanese"
        case .tamil: return "Tamil"
        case .korean: return "Korean"
        case .italian: return "Italian"
        case .german: return "German"
        case .hindi: return "Hindi"
        case .chinese: return "Chinese"
        case .portuguese: return "Portuguese"
        case .telugu: return "Telugu"
        case .vietnamese: return "Vietnamese"
        case .indonesian: return "Indonesian"
        case .arabic: return "Arabic"
        // Previous 10 languages
        case .kannada: return "Kannada"
        case .malayalam: return "Malayalam"
        case .bengali: return "Bengali"
        case .marathi: return "Marathi"
        case .punjabi: return "Punjabi"
        case .dutch: return "Dutch"
        case .swedish: return "Swedish"
        case .thai: return "Thai"
        case .russian: return "Russian"
        case .norwegian: return "Norwegian"
        // Additional 25 languages
        case .gujarati: return "Gujarati"
        case .odia: return "Odia"
        case .assamese: return "Assamese"
        case .konkani: return "Konkani"
        case .sindhi: return "Sindhi"
        case .bhojpuri: return "Bhojpuri"
        case .maithili: return "Maithili"
        case .swahili: return "Swahili"
        case .hebrew: return "Hebrew"
        case .greek: return "Greek"
        case .turkish: return "Turkish"
        case .farsi: return "Farsi"
        case .tagalog: return "Tagalog"
        case .ukrainian: return "Ukrainian"
        case .danish: return "Danish"
        case .xhosa: return "Xhosa"
        case .zulu: return "Zulu"
        case .amharic: return "Amharic"
        case .quechua: return "Quechua"
        case .maori: return "Māori"
        case .cherokee: return "Cherokee"
        case .navajo: return "Navajo"
        case .hawaiian: return "Hawaiian"
        case .inuktitut: return "Inuktitut"
        case .yoruba: return "Yoruba"
        // Additional languages
        case .urdu: return "Urdu"
        case .polish: return "Polish"
        case .czech: return "Czech"
        case .hungarian: return "Hungarian"
        case .romanian: return "Romanian"
        case .bulgarian: return "Bulgarian"
        case .croatian: return "Croatian"
        case .serbian: return "Serbian"
        case .slovak: return "Slovak"
        case .slovenian: return "Slovenian"
        case .estonian: return "Estonian"
        case .latvian: return "Latvian"
        case .lithuanian: return "Lithuanian"
        case .maltese: return "Maltese"
        case .irish: return "Irish"
        case .welsh: return "Welsh"
        case .scots: return "Scots Gaelic"
        case .manx: return "Manx"
        case .cornish: return "Cornish"
        case .breton: return "Breton"
        case .basque: return "Basque"
        case .catalan: return "Catalan"
        case .galician: return "Galician"
        }
    }
    
    /// Determines if this language uses a non-Latin script that requires dedicated Read/Write tabs
    var requiresScriptLiteracy: Bool {
        switch self {
        case .tamil, .hindi, .telugu, .kannada, .malayalam, .bengali, .marathi, .punjabi, .gujarati, .odia, .assamese, .konkani, .sindhi, .bhojpuri, .maithili:
            return true // Indian languages with unique scripts
        case .japanese, .korean, .chinese:
            return true // East Asian scripts
        case .arabic, .farsi, .urdu:
            return true // Arabic script languages
        case .thai:
            return true // Thai script
        case .russian, .bulgarian, .ukrainian, .serbian:
            return true // Cyrillic scripts
        case .greek:
            return true // Greek script
        case .hebrew:
            return true // Hebrew script
        case .amharic:
            return true // Ethiopic script
        case .cherokee:
            return true // Cherokee syllabary
        default:
            return false // Latin script languages
        }
    }

    var flag: String {
        switch self {
        case .french: return "🇫🇷"
        case .english: return "🇺🇸"
        case .spanish: return "🇪🇸"
        case .japanese: return "🇯🇵"
        case .tamil: return "🇮🇳"
        case .korean: return "🇰🇷"
        case .italian: return "🇮🇹"
        case .german: return "🇩🇪"
        case .hindi: return "🇮🇳"
        case .chinese: return "🇨🇳"
        case .portuguese: return "🇵🇹"
        case .telugu: return "🇮🇳"
        case .vietnamese: return "🇻🇳"
        case .indonesian: return "🇮🇩"
        case .arabic: return "🇸🇦"
        // Previous 10 languages
        case .kannada: return "🇮🇳"
        case .malayalam: return "🇮🇳"
        case .bengali: return "🇧🇩"
        case .marathi: return "🇮🇳"
        case .punjabi: return "🇮🇳"
        case .dutch: return "🇳🇱"
        case .swedish: return "🇸🇪"
        case .thai: return "🇹🇭"
        case .russian: return "🇷🇺"
        case .norwegian: return "🇳🇴"
        // Additional 25 languages
        case .gujarati: return "🇮🇳"
        case .odia: return "🇮🇳"
        case .assamese: return "🇮🇳"
        case .konkani: return "🇮🇳"
        case .sindhi: return "🇮🇳"
        case .bhojpuri: return "🇮🇳"
        case .maithili: return "🇮🇳"
        case .swahili: return "🇹🇿"
        case .hebrew: return "🇮🇱"
        case .greek: return "🇬🇷"
        case .turkish: return "🇹🇷"
        case .farsi: return "🇮🇷"
        case .tagalog: return "🇵🇭"
        case .ukrainian: return "🇺🇦"
        case .danish: return "🇩🇰"
        case .xhosa: return "🇿🇦"
        case .zulu: return "🇿🇦"
        case .amharic: return "🇪🇹"
        case .quechua: return "🇵🇪"
        case .maori: return "🇳🇿"
        case .cherokee: return "🇺🇸"
        case .navajo: return "🇺🇸"
        case .hawaiian: return "🇺🇸"
        case .inuktitut: return "🇨🇦"
        case .yoruba: return "🇳🇬"
        // Additional languages
        case .urdu: return "🇵🇰"
        case .polish: return "🇵🇱"
        case .czech: return "🇨🇿"
        case .hungarian: return "🇭🇺"
        case .romanian: return "🇷🇴"
        case .bulgarian: return "🇧🇬"
        case .croatian: return "🇭🇷"
        case .serbian: return "🇷🇸"
        case .slovak: return "🇸🇰"
        case .slovenian: return "🇸🇮"
        case .estonian: return "🇪🇪"
        case .latvian: return "🇱🇻"
        case .lithuanian: return "🇱🇹"
        case .maltese: return "🇲🇹"
        case .irish: return "🇮🇪"
        case .welsh: return "🏴󠁧󠁢󠁷󠁬󠁳󠁿"
        case .scots: return "🏴󠁧󠁢󠁳󠁣󠁴󠁿"
        case .manx: return "🇮🇲"
        case .cornish: return "🏴󠁧󠁢󠁥󠁮󠁧󠁿"
        case .breton: return "🇫🇷"
        case .basque: return "🇪🇸"
        case .catalan: return "🇪🇸"
        case .galician: return "🇪🇸"
        }
    }

    var culturalContext: [String] {
        switch self {
        case .french:
            return ["café", "boulangerie", "marché", "restaurant"]
        case .english:
            return ["office", "airport", "hotel", "university"]
        case .spanish:
            return ["mercado", "restaurante", "oficina", "playa"]
        case .japanese:
            return ["駅", "レストラン", "学校", "病院"]
        case .tamil:
            return ["கோயில்", "கடை", "பள்ளி", "மருத்துவமனை"]
        case .korean:
            return ["카페", "시장", "학교", "병원"]
        case .italian:
            return ["piazza", "ristorante", "mercato", "università"]
        case .german:
            return ["Café", "Markt", "Schule", "Krankenhaus"]
        case .hindi:
            return ["बाज़ार", "रेस्तराँ", "स्कूल", "अस्पताल"]
        case .chinese:
            return ["咖啡厅", "市场", "学校", "医院"]
        case .portuguese:
            return ["café", "mercado", "escola", "hospital"]
        case .telugu:
            return ["కాఫీ షాప్", "మార్కెట్", "పాఠశాల", "ఆసుపత్రి"]
        case .vietnamese:
            return ["quán cà phê", "chợ", "trường học", "bệnh viện"]
        case .indonesian:
            return ["warung", "pasar", "sekolah", "rumah sakit"]
        case .arabic:
            return ["مقهى", "سوق", "مدرسة", "مستشفى"]
        // New 10 languages
        case .kannada:
            return ["ಕಾಫಿ ಶಾಪ್", "ಮಾರುಕಟ್ಟೆ", "ಶಾಲೆ", "ಆಸ್ಪತ್ರೆ"]
        case .malayalam:
            return ["കാഫെ", "മാർക്കറ്റ്", "സ്കൂൾ", "ആശുപത്രി"]
        case .bengali:
            return ["কফি শপ", "বাজার", "স্কুল", "হাসপাতাল"]
        case .marathi:
            return ["कॅफे", "बाजार", "शाळा", "रुग्णालय"]
        case .punjabi:
            return ["ਕੈਫੇ", "ਮਾਰਕੀਟ", "ਸਕੂਲ", "ਹਸਪਤਾਲ"]
        case .dutch:
            return ["café", "markt", "school", "ziekenhuis"]
        case .swedish:
            return ["kafé", "marknad", "skola", "sjukhus"]
        case .thai:
            return ["ร้านกาแฟ", "ตลาด", "โรงเรียน", "โรงพยาบาล"]
        case .russian:
            return ["кафе", "рынок", "школа", "больница"]
        case .norwegian:
            return ["kafé", "marked", "skole", "sykehus"]
        // Additional 25 languages
        case .gujarati:
            return ["કાફે", "બજાર", "શાળા", "હોસ્પિટલ"]
        case .odia:
            return ["କାଫେ", "ବଜାର", "ବିଦ୍ୟାଳୟ", "ଡାକ୍ତରଖାନା"]
        case .assamese:
            return ["কেফে", "বজাৰ", "বিদ্যালয়", "চিকিৎসালয়"]
        case .konkani:
            return ["कॅफे", "बाजार", "शाळा", "रुग्णालय"]
        case .sindhi:
            return ["ڪيفي", "بازار", "اسڪول", "اسپتال"]
        case .bhojpuri:
            return ["कैफे", "बाजार", "स्कूल", "अस्पताल"]
        case .maithili:
            return ["कैफे", "बाजार", "स्कूल", "अस्पताल"]
        case .swahili:
            return ["kahawa", "soko", "shule", "hospitali"]
        case .hebrew:
            return ["בית קפה", "שוק", "בית ספר", "בית חולים"]
        case .greek:
            return ["καφετέρια", "αγορά", "σχολείο", "νοσοκομείο"]
        case .turkish:
            return ["kafe", "pazar", "okul", "hastane"]
        case .farsi:
            return ["کافه", "بازار", "مدرسه", "بیمارستان"]
        case .tagalog:
            return ["kape", "palengke", "paaralan", "ospital"]
        case .ukrainian:
            return ["кафе", "ринок", "школа", "лікарня"]
        case .danish:
            return ["café", "marked", "skole", "hospital"]
        case .xhosa:
            return ["ikofu", "imarike", "isikolo", "isibhedlele"]
        case .zulu:
            return ["ikhofi", "imakethe", "isikole", "isibhedlela"]
        case .amharic:
            return ["ቡና ቤት", "ገበያ", "ትምህርት ቤት", "ሆስፒታል"]
        case .quechua:
            return ["kafe", "qhatu", "yachana wasi", "hampina wasi"]
        case .maori:
            return ["kōhī", "mākete", "kura", "hōhipera"]
        case .cherokee:
            return ["ᎧᏫ", "ᏗᎾᏓᏍᏗ", "ᏗᏕᎶᏆᏍᏗ", "ᏗᏂᎳᏫᏍᏗ"]
        case .navajo:
            return ["koffee", "naʼałkaah", "ółtaʼ", "azeeʼałʼį́"]
        case .hawaiian:
            return ["kope", "mākeke", "kula", "hale māʻi"]
        case .inuktitut:
            return ["ᑲᐱ", "ᓂᐅᕐᕕᒃ", "ᐃᓕᓐᓂᐊᕐᕕᒃ", "ᐋᓐᓂᐊᕐᕕᒃ"]
        case .yoruba:
            return ["kofi", "ọja", "ile-iwe", "ile-iwosan"]
        // Additional languages
        case .urdu:
            return ["کیفے", "بازار", "اسکول", "ہسپتال"]
        case .polish:
            return ["kawiarnia", "rynek", "szkoła", "szpital"]
        case .czech:
            return ["kavárna", "trh", "škola", "nemocnice"]
        case .hungarian:
            return ["kávézó", "piac", "iskola", "kórház"]
        case .romanian:
            return ["cafenea", "piață", "școală", "spital"]
        case .bulgarian:
            return ["кафене", "пазар", "училище", "болница"]
        case .croatian:
            return ["kafić", "tržnica", "škola", "bolnica"]
        case .serbian:
            return ["кафе", "пијаца", "школа", "болница"]
        case .slovak:
            return ["kaviareň", "trh", "škola", "nemocnica"]
        case .slovenian:
            return ["kavarna", "tržnica", "šola", "bolnišnica"]
        case .estonian:
            return ["kohvik", "turg", "kool", "haigla"]
        case .latvian:
            return ["kafejnīca", "tirgus", "skola", "slimnīca"]
        case .lithuanian:
            return ["kavinė", "turgus", "mokykla", "ligoninė"]
        case .maltese:
            return ["kafè", "suq", "skola", "sptar"]
        case .irish:
            return ["caifé", "margadh", "scoil", "ospidéal"]
        case .welsh:
            return ["caffi", "marchnad", "ysgol", "ysbyty"]
        case .scots:
            return ["cafaidh", "margadh", "sgoil", "ospadal"]
        case .manx:
            return ["caffee", "margey", "scoill", "thie lheihys"]
        case .cornish:
            return ["koffi", "marghas", "skol", "chi-heyl"]
        case .breton:
            return ["kafedi", "marc'had", "skol", "ospital"]
        case .basque:
            return ["kafetegia", "merkatua", "eskola", "ospitalea"]
        case .catalan:
            return ["cafeteria", "mercat", "escola", "hospital"]
        case .galician:
            return ["cafetería", "mercado", "escola", "hospital"]
        }
    }

    var writingSystem: String {
        switch self {
        case .arabic, .farsi, .urdu: return "Arabic"
        case .chinese: return "Chinese Characters"
        case .japanese: return "Hiragana/Katakana/Kanji"
        case .korean: return "Hangul"
        case .hindi, .marathi, .bhojpuri, .maithili, .konkani: return "Devanagari"
        case .tamil: return "Tamil"
        case .telugu: return "Telugu"
        case .kannada: return "Kannada"
        case .malayalam: return "Malayalam"
        case .bengali, .assamese: return "Bengali"
        case .punjabi: return "Gurmukhi"
        case .gujarati: return "Gujarati"
        case .odia: return "Odia"
        case .sindhi: return "Arabic/Devanagari"
        case .thai: return "Thai"
        case .russian, .ukrainian, .bulgarian, .serbian: return "Cyrillic"
        case .greek: return "Greek"
        case .hebrew: return "Hebrew"
        case .amharic: return "Ethiopic"
        case .cherokee: return "Cherokee Syllabary"
        case .inuktitut: return "Inuktitut Syllabics"
        default: return "Latin"
        }
    }
}

@Model
final class Achievement {
    var id: UUID
    var type: AchievementType
    var title: String
    var achievementDescription: String
    var iconName: String
    var unlockedDate: Date
    var value: Int
    var userID: UUID

    init(
        id: UUID = UUID(),
        type: AchievementType,
        title: String = "",
        achievementDescription: String = "",
        iconName: String = "",
        unlockedDate: Date = Date(),
        value: Int = 0,
        userID: UUID
    ) {
        self.id = id
        self.type = type
        self.title = title
        self.achievementDescription = achievementDescription
        self.iconName = iconName
        self.unlockedDate = unlockedDate
        self.value = value
        self.userID = userID
    }
}

enum AchievementType: String, CaseIterable, Codable {
    case firstLesson = "first_lesson"
    case weekStreak = "week_streak"
    case monthStreak = "month_streak"
    case hundredLessons = "hundred_lessons"
    case perfectPronunciation = "perfect_pronunciation"
    case culturalExpert = "cultural_expert"
    case polyglot = "polyglot"

    var displayName: String {
        switch self {
        case .firstLesson: return "First Steps"
        case .weekStreak: return "Week Warrior"
        case .monthStreak: return "Month Master"
        case .hundredLessons: return "Century Scholar"
        case .perfectPronunciation: return "Perfect Pronunciation"
        case .culturalExpert: return "Cultural Expert"
        case .polyglot: return "Polyglot"
        }
    }

    var achievementDescription: String {
        switch self {
        case .firstLesson: return "Complete your first lesson"
        case .weekStreak: return "Maintain a 7-day learning streak"
        case .monthStreak: return "Maintain a 30-day learning streak"
        case .hundredLessons: return "Complete 100 lessons"
        case .perfectPronunciation: return "Score 100% on pronunciation"
        case .culturalExpert: return "Complete all cultural simulations"
        case .polyglot: return "Study 3 different languages"
        }
    }

    var icon: String {
        switch self {
        case .firstLesson: return "star.fill"
        case .weekStreak: return "flame.fill"
        case .monthStreak: return "crown.fill"
        case .hundredLessons: return "graduationcap.fill"
        case .perfectPronunciation: return "mic.fill"
        case .culturalExpert: return "globe.asia.australia.fill"
        case .polyglot: return "person.3.fill"
        }
    }
}