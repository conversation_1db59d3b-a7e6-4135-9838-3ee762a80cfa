import Foundation

// MARK: - Lesson Content Models

/// Model for vocabulary items with audio support
struct LessonVocabularyItem: Identifiable, Codable {
    let id: UUID
    let word: String
    let translation: String?
    let pronunciation: String?
    let partOfSpeech: String?
    let example: String?
    let wordAudioURL: String?
    let exampleAudioURL: String?
    
    init(word: String, translation: String? = nil, pronunciation: String? = nil, partOfSpeech: String? = nil, example: String? = nil, wordAudioURL: String? = nil, exampleAudioURL: String? = nil) {
        self.id = UUID()
        self.word = word
        self.translation = translation
        self.pronunciation = pronunciation
        self.partOfSpeech = partOfSpeech
        self.example = example
        self.wordAudioURL = wordAudioURL
        self.exampleAudioURL = exampleAudioURL
    }
}

/// Model for dialogue items in conversations
struct LessonDialogueItem: Identifiable, Codable {
    let id: UUID
    let speaker: String
    let text: String
    let translation: String?
    let pronunciation: String?
    let culturalNote: String?
    let audioURL: String?
    
    init(speaker: String, text: String, translation: String? = nil, pronunciation: String? = nil, culturalNote: String? = nil, audioURL: String? = nil) {
        self.id = UUID()
        self.speaker = speaker
        self.text = text
        self.translation = translation
        self.pronunciation = pronunciation
        self.culturalNote = culturalNote
        self.audioURL = audioURL
    }
}

/// Model for grammar points with examples
struct LessonGrammarPoint: Identifiable, Codable {
    let id: UUID
    let rule: String
    let explanation: String
    let examples: [String]
    let tips: String?
    let examplesAudioURLs: [String]?
    
    init(rule: String, explanation: String, examples: [String] = [], tips: String? = nil, examplesAudioURLs: [String]? = nil) {
        self.id = UUID()
        self.rule = rule
        self.explanation = explanation
        self.examples = examples
        self.tips = tips
        self.examplesAudioURLs = examplesAudioURLs
    }
}

/// Model for lesson exercises with various types
struct LessonExerciseItem: Identifiable, Codable {
    let id: UUID
    let type: String
    let question: String
    let options: [String]
    let correctAnswer: Int
    let explanation: String?
    let points: Int
    let optionsPronunciations: [String]?
    let questionAudioURL: String?
    
    init(type: String, question: String, options: [String] = [], correctAnswer: Int = 0, explanation: String? = nil, points: Int = 10, optionsPronunciations: [String]? = nil, questionAudioURL: String? = nil) {
        self.id = UUID()
        self.type = type
        self.question = question
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.points = points
        self.optionsPronunciations = optionsPronunciations
        self.questionAudioURL = questionAudioURL
    }
} 