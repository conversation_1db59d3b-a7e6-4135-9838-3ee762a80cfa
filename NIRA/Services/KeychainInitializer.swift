//
//  KeychainInitializer.swift
//  NIRA
//
//  Created by Augment Agent on 02/06/2025.
//

import Foundation

// MARK: - Keychain Initialization for Development

struct KeychainInitializer {
    
    /// Initialize keychain with API keys for development
    static func initializeKeychainIfNeeded() {
        #if DEBUG
        // Only run in debug mode for security
        
        // Check if keys are already stored
        if SecureAPIKeys.isConfigured {
            print("✅ Keychain already configured with API keys")
            return
        }
        
        print("🔧 Initializing keychain with API keys...")
        
        do {
            try SecureAPIKeys.storeAPIKeys(
                gemini: "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q",
                supabaseURL: "https://lyaojebttnqilmdosmjk.supabase.co",
                supabaseKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
                openAI: "***********************************************************************************************************************************************************************"
            )
            print("✅ Keychain initialized successfully")
        } catch {
            print("❌ Failed to initialize keychain: \(error)")
            print("💡 App will use fallback values from SecureAPIKeys")
        }
        #endif
    }
}
