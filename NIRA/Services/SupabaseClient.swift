import Foundation
import Combine
import Supabase

// MARK: - Configuration
// Using centralized API configuration

@MainActor
class NIRASupabaseClient: ObservableObject {
    static let shared = NIRASupabaseClient()

    let client: SupabaseClient
    @Published var session: Session?
    @Published var currentUser: Auth.User?
    @Published var isConnected = false
    @Published var lastError: Error?

    private var configuredURL: String = ""

    private init() {
        // Initialize Supabase client with secure configuration
        let supabaseURL: String
        let supabaseKey: String

        // Try to get from SecureAPIKeys first, fallback to hardcoded APIKeys
        let secureURL = SecureAPIKeys.supabaseURL
        let secureKey = SecureAPIKeys.supabaseAnonKey

        print("🔍 NIRASupabaseClient init - SecureAPIKeys check:")
        print("   URL: '\(secureURL.isEmpty ? "EMPTY" : secureURL)'")
        print("   Key: '\(secureKey.isEmpty ? "EMPTY" : String(secureKey.prefix(20)))...'")

        if !secureURL.isEmpty && !secureKey.isEmpty &&
           !secureURL.contains("PLACEHOLDER") && !secureKey.contains("PLACEHOLDER") {
            supabaseURL = secureURL
            supabaseKey = secureKey
            print("✅ Using secure API keys from keychain")
        } else {
            // Fallback to hardcoded APIKeys
            print("⚠️ SecureAPIKeys not available, falling back to hardcoded APIKeys")
            print("   Reason: URL empty=\(secureURL.isEmpty), Key empty=\(secureKey.isEmpty)")
            print("   URL contains PLACEHOLDER=\(secureURL.contains("PLACEHOLDER")), Key contains PLACEHOLDER=\(secureKey.contains("PLACEHOLDER"))")
            
            // Use hardcoded APIKeys as fallback
            if APIKeys.isConfigured {
                supabaseURL = APIKeys.supabaseURL
                supabaseKey = APIKeys.supabaseAnonKey
                print("✅ Using hardcoded API keys from APIKeys.swift")
            } else {
                // Last resort - use placeholder values that won't crash
                print("⚠️ Using development mode - Supabase features will be limited")
                supabaseURL = "https://placeholder.supabase.co"
                supabaseKey = "placeholder_key"
            }
        }

        guard let url = URL(string: supabaseURL) else {
            print("❌ Invalid Supabase URL: \(supabaseURL)")
            // Use a valid placeholder URL to prevent crash
            let fallbackURL = URL(string: "https://placeholder.supabase.co")!
            self.client = SupabaseClient(
                supabaseURL: fallbackURL,
                supabaseKey: "placeholder_key"
            )
            return
        }

        self.client = SupabaseClient(
            supabaseURL: url,
            supabaseKey: supabaseKey
        )

        // Store the configured URL for later checks
        self.configuredURL = supabaseURL

        // Start background tasks
        self.startBackgroundTasks()
    }

    private func startBackgroundTasks() {
        setupAuthListener()
        checkConnection()
    }

    // MARK: - Authentication

    func signUp(email: String, password: String) async throws {
        try await client.auth.signUp(email: email, password: password)
        // The auth state listener will handle updating session and user
    }

    func signIn(email: String, password: String) async throws {
        try await client.auth.signIn(email: email, password: password)
        // The auth state listener will handle updating session and user
    }

    func signOut() async throws {
        try await client.auth.signOut()
        self.session = nil
        self.currentUser = nil
    }

    func resetPassword(email: String) async throws {
        try await client.auth.resetPasswordForEmail(email)
    }

    // MARK: - Database Operations

    func getLessons(language: String? = nil, level: String? = nil) async throws -> [SupabaseLesson] {
        // If no language specified, return empty array
        guard let language = language else {
            print("⚠️ No language specified for lesson query")
            return []
        }

        // Convert language name to database code
        let languageCode = getLanguageCode(for: language)
        print("🔍 Converting language '\(language)' to code '\(languageCode)'")

        // First get the language ID
        let languageResponse: [SupabaseLanguageModel] = try await client
            .from("languages")
            .select()
            .eq("code", value: languageCode)
            .execute()
            .value

        guard let languageId = languageResponse.first?.id else {
            print("⚠️ Language '\(language)' not found in database")
            return []
        }

        // Get learning paths for this language
        let pathsResponse: [SupabaseLearningPath] = try await client
            .from("learning_paths")
            .select()
            .eq("language_id", value: languageId.uuidString)
            .eq("is_active", value: true)
            .execute()
            .value

        let pathIds = pathsResponse.map { $0.id.uuidString }

        guard !pathIds.isEmpty else {
            print("⚠️ No learning paths found for language '\(language)'")
            return []
        }

        // Get lessons for these paths using the actual database schema
        let baseQuery = client
            .from("lessons")
            .select("*")
            .in("path_id", values: pathIds)
            .eq("is_active", value: true)

        // Filter by level if specified
        let finalQuery: PostgrestFilterBuilder
        if let level = level, level != "All" {
            let difficultyLevel = getDifficultyNumber(for: level)
            finalQuery = baseQuery.eq("difficulty_score", value: difficultyLevel)
        } else {
            finalQuery = baseQuery
        }

        let response: [SupabaseLesson] = try await finalQuery
            .order("sequence_order", ascending: true)
            .execute().value

        // Get the language name for the response
        let languageName = languageResponse.first?.name ?? language.capitalized

        print("✅ Loaded \(response.count) lessons for \(language) (\(languageName))" + (level != nil ? " at level \(level!)" : ""))
        return response
    }

    private func getDifficultyNumber(for level: String) -> Int {
        switch level {
        case "A1": return 1
        case "A2": return 2
        case "B1": return 3
        case "B2": return 4
        case "C1": return 5
        case "C2": return 6
        default: return 1
        }
    }

    private func getLanguageCode(for language: String) -> String {
        // Convert from app language names to database language codes
        switch language.lowercased() {
        case "english": return "en"
        case "spanish": return "es"
        case "french": return "fr"
        case "german": return "de"
        case "italian": return "it"
        case "portuguese": return "pt"
        case "japanese": return "ja"
        case "korean": return "ko"
        case "chinese": return "zh"
        case "arabic": return "ar"
        case "hindi": return "hi"
        case "tamil": return "ta"
        case "telugu": return "te"
        case "vietnamese": return "vi"
        case "indonesian": return "id"
        default: return language.lowercased() // fallback to original
        }
    }

    func getLesson(id: String) async throws -> SupabaseLesson? {
        let response: [SupabaseLesson] = try await client
            .from("lessons")
            .select()
            .eq("id", value: id)
            .eq("is_active", value: true)
            .execute()
            .value

        return response.first
    }

    // MARK: - Progress Tracking

    func updateProgress(
        lessonId: String,
        status: String,
        score: Int,
        timeSpent: Int
    ) async throws {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let progressData: [String: String?] = [
            "user_id": userId.uuidString,
            "lesson_id": lessonId,
            "status": status,
            "score": String(score),
            "time_spent_seconds": String(timeSpent),
            "completed_at": status == "completed" ? ISO8601DateFormatter().string(from: Date()) : nil
        ]

        _ = try await client
            .from("user_progress")
            .upsert(progressData)
            .execute()
    }

    func getUserProgress() async throws -> [SupabaseUserProgress] {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let response: [SupabaseUserProgress] = try await client
            .from("user_progress")
            .select("*, lessons(*)")
            .eq("user_id", value: userId.uuidString)
            .execute()
            .value

        return response
    }

    // MARK: - User Profile

    func updateUserProfile(
        firstName: String? = nil,
        lastName: String? = nil,
        preferredLanguages: [String]? = nil
    ) async throws {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        var updateData: [String: String] = [:]
        if let firstName = firstName { updateData["first_name"] = firstName }
        if let lastName = lastName { updateData["last_name"] = lastName }
        if let languages = preferredLanguages {
            updateData["preferred_languages"] = languages.joined(separator: ",")
        }
        updateData["last_active_date"] = ISO8601DateFormatter().string(from: Date())

        _ = try await client
            .from("users")
            .update(updateData)
            .eq("id", value: userId.uuidString)
            .execute()
    }

    func getUserProfile() async throws -> SupabaseUserProfile? {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let response: [SupabaseUserProfile] = try await client
            .from("users")
            .select()
            .eq("id", value: userId.uuidString)
            .execute()
            .value

        return response.first
    }

    // MARK: - Simulation Methods
    
    func getSimulationPersonas() async throws -> [SupabaseSimulationPersona] {
        let response: [SupabaseSimulationPersona] = try await client
            .from("simulation_personas")
            .select("*")
            .eq("is_active", value: true)
            .order("sort_order", ascending: true)
            .execute()
            .value
        
        print("✅ Loaded \(response.count) simulation personas")
        return response
    }
    
    func getSimulations(personaId: UUID? = nil, languageId: UUID? = nil) async throws -> [SupabaseSimulation] {
        var query = client
            .from("simulations")
            .select("*")
            .eq("is_active", value: true)
        
        if let personaId = personaId {
            query = query.eq("persona_id", value: personaId.uuidString)
        }
        
        if let languageId = languageId {
            query = query.eq("language_id", value: languageId.uuidString)
        }
        
        let response: [SupabaseSimulation] = try await query
            .order("created_at", ascending: false)
            .execute()
            .value
        
        print("✅ Loaded \(response.count) simulations")
        return response
    }

    // MARK: - FSRS (Spaced Repetition) Methods
    
    func fetchReviewSchedule() async throws -> [ReviewItem] {
        guard currentUser != nil else { throw SupabaseError.notAuthenticated }
        
        let response: [ReviewScheduleRow] = try await client
            .from("review_schedule")
            .select()
            .eq("user_id", value: currentUser?.id.uuidString ?? "")
            .order("next_review_date", ascending: true)
            .execute()
            .value
        
        return response.map { row in
            ReviewItem(
                id: row.id.uuidString,
                learningItemId: row.learning_item_id.uuidString,
                cardId: row.card_id.uuidString,
                nextReviewDate: row.next_review_date,
                difficulty: row.difficulty,
                stability: row.stability,
                retrievability: row.retrievability
            )
        }
    }
    
    func fetchCard(for learningItemId: String) async -> FSRSCard? {
        guard currentUser != nil else { return nil }
        
        do {
            let response: [FSRSCardRow] = try await client
                .from("fsrs_cards")
                .select()
                .eq("user_id", value: currentUser?.id.uuidString ?? "")
                .eq("learning_item_id", value: learningItemId)
                .execute()
                .value
            
            return response.first?.toFSRSCard()
        } catch {
            print("Failed to fetch FSRS card: \(error)")
            return nil
        }
    }
    
    func saveCard(_ card: FSRSCard) async throws {
        guard currentUser != nil else { throw SupabaseError.notAuthenticated }
        
        let cardRow = FSRSCardRow(from: card, userId: UUID(uuidString: currentUser!.id.uuidString) ?? UUID())
        
        try await client
            .from("fsrs_cards")
            .upsert(cardRow)
            .execute()
    }
    
    func saveReviewLog(_ log: ReviewLog) async throws {
        guard currentUser != nil else { throw SupabaseError.notAuthenticated }
        
        let logRow = ReviewLogRow(from: log, userId: UUID(uuidString: currentUser!.id.uuidString) ?? UUID())
        
        try await client
            .from("fsrs_review_logs")
            .insert(logRow)
            .execute()
    }
    
    func fetchReviewLogs(lastDays: Int) async throws -> [ReviewLog] {
        guard currentUser != nil else { throw SupabaseError.notAuthenticated }
        
        let startDate = Calendar.current.date(byAdding: .day, value: -lastDays, to: Date())!
        
        let response: [ReviewLogRow] = try await client
            .from("fsrs_review_logs")
            .select()
            .eq("user_id", value: currentUser?.id.uuidString ?? "")
            .gte("review_time", value: startDate.ISO8601String())
            .order("review_time", ascending: false)
            .execute()
            .value
        
        return response.map { $0.toReviewLog() }
    }
    
    // MARK: - Micro-Assessment Methods
    
    func saveAssessmentResponse(_ response: MicroAssessmentResponse) async throws {
        guard currentUser != nil else { throw SupabaseError.notAuthenticated }
        
        let responseRow = AssessmentResponseRow(from: response, userId: UUID(uuidString: currentUser!.id.uuidString) ?? UUID())
        
        try await client
            .from("assessment_responses")
            .insert(responseRow)
            .execute()
    }
    
    func saveCompletedUnit(_ unit: CompletedLearningUnit) async throws {
        guard currentUser != nil else { throw SupabaseError.notAuthenticated }
        
        let unitRow = LearningUnitCompletionRow(from: unit, userId: UUID(uuidString: currentUser!.id.uuidString) ?? UUID())
        
        try await client
            .from("learning_unit_completions")
            .insert(unitRow)
            .execute()
    }
    
    func fetchRecentAssessmentScores(limit: Int) async throws -> [AssessmentScore] {
        guard currentUser != nil else { throw SupabaseError.notAuthenticated }
        
        let response: [AssessmentReportRow] = try await client
            .from("assessment_reports")
            .select()
            .eq("user_id", value: currentUser?.id.uuidString ?? "")
            .order("generated_at", ascending: false)
            .limit(limit)
            .execute()
            .value
        
        return response.map { row in
            AssessmentScore(
                assessmentId: row.assessment_id.uuidString,
                overallScore: row.overall_score,
                skillScores: row.skill_scores,
                completedAt: row.generated_at
            )
        }
    }

    // MARK: - Lesson Content Fetching from Normalized Tables
    
    func getLessonVocabulary(lessonId: UUID) async throws -> [LessonVocabularyItem] {
        let response: [[String: SupabaseAnyCodable]] = try await client
            .from("lesson_vocabulary")
            .select("*")
            .eq("lesson_id", value: lessonId.uuidString)
            .order("word_order", ascending: true)
            .execute()
            .value
        
        return response.compactMap { vocabDict in
            guard let tamilWord = vocabDict["word_target_language"]?.value as? String,
                  let englishTranslation = vocabDict["word_english"]?.value as? String else { 
                return nil 
            }
            
            // Build comprehensive example with Tamil, English, and romanization
            var fullExample: String? = nil
            if let exampleTamil = vocabDict["example_sentence_target_language"]?.value as? String,
               let exampleEng = vocabDict["example_sentence_english"]?.value as? String,
               let exampleRoman = vocabDict["example_sentence_romanized"]?.value as? String {
                fullExample = "\(exampleTamil)\n\(exampleEng)\n[\(exampleRoman)]"
            } else if let exampleTamil = vocabDict["example_sentence_target_language"]?.value as? String,
                      let exampleEng = vocabDict["example_sentence_english"]?.value as? String {
                fullExample = "\(exampleTamil)\n\(exampleEng)"
            } else if let exampleEng = vocabDict["example_sentence_english"]?.value as? String {
                fullExample = exampleEng
            }
            
            return LessonVocabularyItem(
                word: tamilWord,  // Show Tamil word as primary
                translation: englishTranslation,  // English as translation
                pronunciation: vocabDict["word_romanized"]?.value as? String,
                partOfSpeech: vocabDict["part_of_speech"]?.value as? String,
                example: fullExample,
                wordAudioURL: vocabDict["word_audio_url"]?.value as? String,
                exampleAudioURL: vocabDict["example_audio_url"]?.value as? String
            )
        }
    }
    
    func getLessonConversations(lessonId: UUID) async throws -> [LessonDialogueItem] {
        let response: [[String: SupabaseAnyCodable]] = try await client
            .from("lesson_conversations")
            .select("*")
            .eq("lesson_id", value: lessonId.uuidString)
            .order("conversation_order", ascending: true)
            .execute()
            .value
        
        var dialogues: [LessonDialogueItem] = []
        
        for convDict in response {
            // Parse conversation_content JSONB array
            if let contentArray = convDict["conversation_content"]?.value as? [[String: Any]] {
                for exchange in contentArray {
                    if let text = exchange["text_english"] as? String,
                       let translation = exchange["text_tamil"] as? String,
                       let speaker = exchange["speaker"] as? String {
                        
                        let dialogue = LessonDialogueItem(
                            speaker: speaker,
                            text: text,
                            translation: translation,
                            pronunciation: exchange["text_romanized"] as? String,
                            culturalNote: nil,
                            audioURL: exchange["audio_url"] as? String ?? convDict["audio_url"]?.value as? String
                        )
                        dialogues.append(dialogue)
                    }
                }
            }
            // Handle Lesson 2 format: conversation_content has "lines" array
            else if let contentDict = convDict["conversation_content"]?.value as? [String: Any],
                    let linesArray = contentDict["lines"] as? [[String: Any]] {
                for exchange in linesArray {
                    if let text = exchange["english"] as? String,
                       let translation = exchange["tamil"] as? String,
                       let speaker = exchange["speaker"] as? String {
                        
                        let dialogue = LessonDialogueItem(
                            speaker: speaker,
                            text: text,
                            translation: translation,
                            pronunciation: exchange["romanized"] as? String,
                            culturalNote: nil,
                            audioURL: exchange["audio_url"] as? String
                        )
                        dialogues.append(dialogue)
                    }
                }
            }
            // Fallback: Handle if content comes as string (legacy format)
            else if let contentStr = convDict["conversation_content"]?.value as? String,
                    let contentData = contentStr.data(using: .utf8),
                    let exchanges = try? JSONSerialization.jsonObject(with: contentData) as? [[String: Any]] {
                
                for exchange in exchanges {
                    if let text = exchange["text_english"] as? String ?? exchange["english"] as? String,
                       let translation = exchange["text_tamil"] as? String ?? exchange["tamil"] as? String,
                       let speaker = exchange["speaker"] as? String {
                        
                        let dialogue = LessonDialogueItem(
                            speaker: speaker,
                            text: text,
                            translation: translation,
                            pronunciation: exchange["text_romanized"] as? String ?? exchange["romanized"] as? String,
                            culturalNote: nil,
                            audioURL: exchange["audio_url"] as? String ?? convDict["audio_url"]?.value as? String
                        )
                        dialogues.append(dialogue)
                    }
                }
            }
        }
        
        return dialogues
    }
    
    func getLessonGrammar(lessonId: UUID) async throws -> [LessonGrammarPoint] {
        let response: [[String: SupabaseAnyCodable]] = try await client
            .from("lesson_grammar")
            .select("*")
            .eq("lesson_id", value: lessonId.uuidString)
            .order("grammar_order", ascending: true)
            .execute()
            .value
        
        return response.compactMap { grammarDict in
            guard let rule = grammarDict["concept_name_english"]?.value as? String,
                  let explanation = grammarDict["explanation_english"]?.value as? String else { 
                return nil 
            }
            
            // Build comprehensive explanation with Tamil and romanization
            var fullExplanation = explanation
            if let explanationTamil = grammarDict["explanation_target_language"]?.value as? String,
               let explanationRoman = grammarDict["explanation_romanized"]?.value as? String {
                fullExplanation = "\(explanation)\n\nதமிழ்: \(explanationTamil)\n(\(explanationRoman))"
            } else if let explanationTamil = grammarDict["explanation_target_language"]?.value as? String {
                fullExplanation = "\(explanation)\n\nதமிழ்: \(explanationTamil)"
            }
            
            // Parse examples JSONB array
            var examples: [String] = []
            var exampleAudioURLs: [String] = []
            if let examplesArray = grammarDict["examples"]?.value as? [[String: Any]] {
                examples = examplesArray.compactMap { example in
                    // Collect audio URLs if available
                    if let audioURL = example["audio_url"] as? String {
                        exampleAudioURLs.append(audioURL)
                    } else {
                        exampleAudioURLs.append("") // Placeholder for missing audio
                    }
                    
                    if let english = example["english"] as? String,
                       let tamil = example["tamil"] as? String,
                       let romanized = example["romanized"] as? String {
                        return "\(english)\n\(tamil)\n(\(romanized))"
                    }
                    return example["english"] as? String
                }
            }
            // Fallback: Handle if examples come as string (legacy format)
            else if let examplesStr = grammarDict["examples"]?.value as? String,
                    let examplesData = examplesStr.data(using: .utf8),
                    let examplesArray = try? JSONSerialization.jsonObject(with: examplesData) as? [[String: Any]] {
                examples = examplesArray.compactMap { $0["english"] as? String }
            }
            
            return LessonGrammarPoint(
                rule: rule,
                explanation: fullExplanation,
                examples: examples,
                tips: nil,
                examplesAudioURLs: exampleAudioURLs.isEmpty ? nil : exampleAudioURLs
            )
        }
    }
    
    func getLessonExercises(lessonId: UUID) async throws -> [LessonExerciseItem] {
        let response: [[String: SupabaseAnyCodable]] = try await client
            .from("lesson_exercises")
            .select("*")
            .eq("lesson_id", value: lessonId.uuidString)
            .order("exercise_order", ascending: true)
            .execute()
            .value
        
        return response.compactMap { exerciseDict in
            guard let question = exerciseDict["title_english"]?.value as? String,
                  let type = exerciseDict["exercise_type"]?.value as? String else { 
                return nil 
            }
            
            // Parse exercise_content JSONB object
            var options: [String] = []
            var correctAnswer = 0
            var enhancedQuestion = question
            var pronunciations: [String] = []
            
            // Get the correct answer from the database
            if let correctAnswerValue = exerciseDict["correct_answer"]?.value as? Int {
                correctAnswer = correctAnswerValue
            }
            
            // Handle JSONB object directly
            if let content = exerciseDict["exercise_content"]?.value as? [String: Any] {
                // Handle questions field if present (common structure)
                if let questionsArray = content["questions"] as? [[String: Any]] {
                    options = questionsArray.compactMap { question in
                        return question["question_english"] as? String ?? question["question"] as? String
                    }
                }
                // Handle options field if present
                else if let optionsArray = content["options"] as? [[String: Any]] {
                    options = optionsArray.compactMap { option in
                        if let tamil = option["tamil"] as? String,
                           let english = option["english"] as? String {
                            let romanized = option["romanized"] as? String ?? ""
                            if !romanized.isEmpty {
                                pronunciations.append(romanized)
                                return "\(tamil) (\(english))"
                            } else {
                                return "\(tamil) (\(english))"
                            }
                        }
                        return option["text"] as? String ?? option["option"] as? String
                    }
                }
                // Handle items field (legacy structure)
                else if let items = content["items"] as? [[String: Any]] {
                    options = items.compactMap { item in
                        if let tamil = item["tamil"] as? String,
                           let english = item["english"] as? String {
                            let romanized = item["romanized"] as? String ?? ""
                            if !romanized.isEmpty {
                                pronunciations.append(romanized)
                            }
                            return "\(tamil) (\(english))"
                        }
                        return item["text"] as? String ?? item["english"] as? String
                    }
                }
                // Handle simple array of strings
                else if let simpleOptions = content["options"] as? [String] {
                    options = simpleOptions
                }
            }
            // Fallback: Handle if content comes as string (legacy format)
            else if let contentStr = exerciseDict["exercise_content"]?.value as? String,
                    let contentData = contentStr.data(using: .utf8),
                    let content = try? JSONSerialization.jsonObject(with: contentData) as? [String: Any] {
                
                if let items = content["items"] as? [[String: Any]] {
                    options = items.compactMap { $0["english"] as? String }
                } else if let simpleOptions = content["options"] as? [String] {
                    options = simpleOptions
                }
            }
            
            // Ensure we have valid options and correct answer is within bounds
            if options.isEmpty {
                print("⚠️ Exercise \(question) has no valid options")
                return nil
            }
            
            if correctAnswer >= options.count {
                print("⚠️ Exercise \(question) has invalid correct answer index: \(correctAnswer) for \(options.count) options")
                correctAnswer = 0 // Default to first option
            }
            
            // Enhanced question with Tamil text and audio support
            if let questionTamil = exerciseDict["title_target_language"]?.value as? String {
                enhancedQuestion = "\(question)\n\(questionTamil)"
            }
            
            return LessonExerciseItem(
                type: type,
                question: enhancedQuestion,
                options: options,
                correctAnswer: correctAnswer,
                explanation: exerciseDict["instructions_english"]?.value as? String,
                points: exerciseDict["max_score"]?.value as? Int ?? 10,
                optionsPronunciations: pronunciations.isEmpty ? nil : pronunciations,
                questionAudioURL: exerciseDict["audio_url"]?.value as? String
            )
        }
    }
    
    func getLessonContentComplete(lessonId: UUID) async throws -> (
        vocabulary: [LessonVocabularyItem],
        conversations: [LessonDialogueItem], 
        grammar: [LessonGrammarPoint],
        exercises: [LessonExerciseItem]
    ) {
        // Fetch all content types in parallel for efficiency
        async let vocabulary = getLessonVocabulary(lessonId: lessonId)
        async let conversations = getLessonConversations(lessonId: lessonId)
        async let grammar = getLessonGrammar(lessonId: lessonId)
        async let exercises = getLessonExercises(lessonId: lessonId)
        
        return try await (
            vocabulary: vocabulary,
            conversations: conversations,
            grammar: grammar,
            exercises: exercises
        )
    }

    // MARK: - Private Methods

    private func setupAuthListener() {
        Task {
            await observeAuthStateChanges()
        }
    }

    private func observeAuthStateChanges() async {
        for await state in client.auth.authStateChanges {
            let session = state.session
            let user = session?.user
            updateAuthState(session: session, user: user)
        }
    }

    @MainActor
    private func updateAuthState(session: Session?, user: Auth.User?) {
        self.session = session
        self.currentUser = user
    }

    private func checkConnection() {
        Task { [weak self] in
            guard let self = self else { return }

            // Skip connection check if using placeholder values
            if self.configuredURL.contains("placeholder") {
                await MainActor.run {
                    self.isConnected = false
                    print("📱 Running in development mode - Supabase features disabled")
                }
                return
            }

            // Try multiple connection checks in order of preference
            var connectionSuccessful = false

            // First, try a simple auth check (always available)
            do {
                _ = try await self.client.auth.session
                connectionSuccessful = true
                print("✅ Supabase connection established (auth endpoint)")
            } catch {
                print("⚠️ Auth endpoint check failed: \(error.localizedDescription)")
            }

            // If auth works, try to check if database is accessible
            if connectionSuccessful {
                do {
                    // Try to access a system table that should always exist
                    _ = try await self.client
                        .rpc("version")
                        .execute()
                    print("✅ Supabase database accessible")
                } catch {
                    print("⚠️ Database access limited: \(error.localizedDescription)")
                    print("💡 This is normal if your database schema isn't set up yet")
                }
            }

            await MainActor.run {
                self.isConnected = connectionSuccessful
                if connectionSuccessful {
                    print("🌐 Supabase fully connected and ready")
                } else {
                    print("❌ Supabase connection failed")
                    print("🔧 Check your Supabase URL and API key configuration")
                }
            }
        }
    }
}

// MARK: - Error Types

enum SupabaseError: Error, Equatable {
    case notAuthenticated
    case invalidResponse
    case networkError(Error)

    var localizedDescription: String {
        switch self {
        case .notAuthenticated:
            return "User not authenticated"
        case .invalidResponse:
            return "Invalid response from server"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }

    static func == (lhs: SupabaseError, rhs: SupabaseError) -> Bool {
        switch (lhs, rhs) {
        case (.notAuthenticated, .notAuthenticated):
            return true
        case (.invalidResponse, .invalidResponse):
            return true
        case (.networkError, .networkError):
            return true
        default:
            return false
        }
    }
}

// MARK: - Helper Types

struct SupabaseAnyCodable: Codable {
    let value: Any

    init(_ value: Any) {
        self.value = value
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let intValue = try? container.decode(Int.self) {
            value = intValue
        } else if let doubleValue = try? container.decode(Double.self) {
            value = doubleValue
        } else if let stringValue = try? container.decode(String.self) {
            value = stringValue
        } else if let boolValue = try? container.decode(Bool.self) {
            value = boolValue
        } else if let arrayValue = try? container.decode([SupabaseAnyCodable].self) {
            value = arrayValue.map { $0.value }
        } else if let dictValue = try? container.decode([String: SupabaseAnyCodable].self) {
            value = dictValue.mapValues { $0.value }
        } else if container.decodeNil() {
            value = NSNull()
        } else {
            value = NSNull()
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        if let intValue = value as? Int {
            try container.encode(intValue)
        } else if let doubleValue = value as? Double {
            try container.encode(doubleValue)
        } else if let stringValue = value as? String {
            try container.encode(stringValue)
        } else if let boolValue = value as? Bool {
            try container.encode(boolValue)
        } else if let arrayValue = value as? [Any] {
            let encodableArray = arrayValue.map { SupabaseAnyCodable($0) }
            try container.encode(encodableArray)
        } else if let dictValue = value as? [String: Any] {
            let encodableDict = dictValue.mapValues { SupabaseAnyCodable($0) }
            try container.encode(encodableDict)
        } else {
            try container.encodeNil()
        }
    }
}

// MARK: - FSRS Database Models

struct ReviewScheduleRow: Codable {
    let id: UUID
    let user_id: UUID
    let learning_item_id: UUID
    let card_id: UUID
    let next_review_date: Date
    let difficulty: Double
    let stability: Double
    let retrievability: Double
    let created_at: Date?
    let updated_at: Date?
}

struct FSRSCardRow: Codable {
    let id: UUID
    let learning_item_id: String
    let user_id: UUID
    let due: Date
    let stability: Double
    let difficulty: Double
    let elapsed_days: Int
    let scheduled_days: Int
    let reps: Int
    let lapses: Int
    let state: String
    let last_review: Date
    let retrievability: Double
    let created_at: Date?
    let updated_at: Date?
    
    init(from card: FSRSCard, userId: UUID) {
        self.id = UUID(uuidString: card.id) ?? UUID()
        self.learning_item_id = card.learningItemId
        self.user_id = userId
        self.due = card.due
        self.stability = card.stability
        self.difficulty = card.difficulty
        self.elapsed_days = card.elapsedDays
        self.scheduled_days = card.scheduledDays
        self.reps = card.reps
        self.lapses = card.lapses
        self.state = card.state.rawValue
        self.last_review = card.lastReview
        self.retrievability = card.retrievability
        self.created_at = nil
        self.updated_at = nil
    }
    
    func toFSRSCard() -> FSRSCard {
        return FSRSCard(
            id: id.uuidString,
            learningItemId: learning_item_id,
            due: due,
            stability: stability,
            difficulty: difficulty,
            elapsedDays: elapsed_days,
            scheduledDays: scheduled_days,
            reps: reps,
            lapses: lapses,
            state: CardState(rawValue: state) ?? .new,
            lastReview: last_review,
            retrievability: retrievability
        )
    }
}

struct ReviewLogRow: Codable {
    let id: UUID
    let card_id: UUID
    let user_id: UUID
    let rating: Int
    let state: String
    let due: Date
    let stability: Double
    let difficulty: Double
    let elapsed_days: Int
    let last_elapsed_days: Int
    let scheduled_days: Int
    let review_time: Date
    let response_time_ms: Int?
    let created_at: Date?
    
    private enum CodingKeys: String, CodingKey {
        case id, card_id, user_id, rating, state, due, stability, difficulty
        case elapsed_days, last_elapsed_days, scheduled_days, review_time
        case response_time_ms, created_at
    }
    
    init(from log: ReviewLog, userId: UUID) {
        self.id = UUID(uuidString: log.id) ?? UUID()
        self.card_id = UUID(uuidString: log.cardId) ?? UUID()
        self.user_id = userId
        self.rating = log.rating.rawValue
        self.state = log.state.rawValue
        self.due = log.due
        self.stability = log.stability
        self.difficulty = log.difficulty
        self.elapsed_days = log.elapsedDays
        self.last_elapsed_days = log.lastElapsedDays
        self.scheduled_days = log.scheduledDays
        self.review_time = log.review
        self.response_time_ms = nil
        self.created_at = nil
    }
    
    func toReviewLog() -> ReviewLog {
        return ReviewLog(
            id: id.uuidString,
            cardId: card_id.uuidString,
            rating: ReviewRating(rawValue: rating) ?? .good,
            state: CardState(rawValue: state) ?? .new,
            due: due,
            stability: stability,
            difficulty: difficulty,
            elapsedDays: elapsed_days,
            lastElapsedDays: last_elapsed_days,
            scheduledDays: scheduled_days,
            review: review_time
        )
    }
}

// MARK: - Assessment Database Models

struct AssessmentResponseRow: Codable {
    let id: UUID
    let assessment_id: UUID
    let item_id: UUID
    let user_id: UUID
    let user_answer: String
    let correct_answer: String
    let is_correct: Bool
    let time_spent: Int
    let attempts: Int
    let confidence_level: Double?
    let created_at: Date?
    
    private enum CodingKeys: String, CodingKey {
        case id, assessment_id, item_id, user_id, user_answer, correct_answer
        case is_correct, time_spent, attempts, confidence_level, created_at
    }
    
    init(from response: MicroAssessmentResponse, userId: UUID) {
        self.id = UUID(uuidString: response.id) ?? UUID()
        self.assessment_id = UUID(uuidString: response.assessmentId) ?? UUID()
        self.item_id = UUID(uuidString: response.itemId) ?? UUID()
        self.user_id = userId
        self.user_answer = response.userAnswer
        self.correct_answer = response.correctAnswer
        self.is_correct = response.isCorrect
        self.time_spent = Int(response.timeSpent * 1000) // Convert to milliseconds
        self.attempts = 1
        self.confidence_level = nil
        self.created_at = nil
    }
}

struct LearningUnitCompletionRow: Codable {
    let id: UUID
    let user_id: UUID
    let lesson_id: UUID?
    let unit_id: String
    let unit_type: String
    let accuracy_score: Double
    let completion_time: Int
    let attempts_needed: Int
    let difficulty_level: Int
    let engagement_score: Double
    let completed_at: Date
    
    private enum CodingKeys: String, CodingKey {
        case id, user_id, lesson_id, unit_id, unit_type, accuracy_score
        case completion_time, attempts_needed, difficulty_level, engagement_score, completed_at
    }
    
    init(from unit: CompletedLearningUnit, userId: UUID) {
        self.id = UUID(uuidString: unit.id) ?? UUID()
        self.user_id = userId
        self.lesson_id = nil
        self.unit_id = unit.id
        self.unit_type = unit.type.rawValue
        self.accuracy_score = unit.performance.accuracyScore
        self.completion_time = Int(unit.performance.completionTime)
        self.attempts_needed = unit.performance.attemptsNeeded
        self.difficulty_level = unit.performance.currentDifficulty
        self.engagement_score = unit.performance.engagementScore
        self.completed_at = unit.completedAt
    }
}

struct AssessmentReportRow: Codable {
    let id: UUID
    let assessment_id: UUID
    let user_id: UUID
    let overall_score: Double
    let total_time_spent: Int
    let completion_percentage: Double
    let skill_scores: [String: Double]
    let improved_areas: [String]
    let areas_for_focus: [String]
    let generated_at: Date
}

// MARK: - Date Extensions

extension Date {
    func ISO8601String() -> String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
}