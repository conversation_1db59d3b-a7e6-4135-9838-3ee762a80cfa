//
//  AdaptiveLearningServiceEnhanced.swift
//  NIRA
//
//  Enhanced Adaptive Learning Service for Real-time Learning Optimization
//  Based on Language Learning Content Development Standards v2.0
//

import Foundation
import Combine
import SwiftUI

class AdaptiveLearningService: ObservableObject {
    static let shared = AdaptiveLearningService()
    
    @Published var currentSession: AdaptiveSession?
    @Published var isSessionActive: Bool = false
    @Published var learningMetrics: AdaptiveLearningMetrics?
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupMetricsUpdates()
    }
    
    private func setupMetricsUpdates() {
        // Update metrics every few seconds during active sessions
        Timer.publish(every: 3.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                if self?.isSessionActive == true {
                    self?.updateLearningMetrics()
                }
            }
            .store(in: &cancellables)
    }
    
    func startSession() async {
        isSessionActive = true
        currentSession = AdaptiveSession(
            id: UUID(),
            startTime: Date(),
            learnerID: "current_user",
            language: .tamil
        )
        updateLearningMetrics()
    }
    
    func pauseSession() async {
        isSessionActive = false
        updateLearningMetrics()
    }
    
    func endSession() async {
        isSessionActive = false
        currentSession = nil
        learningMetrics = nil
    }
    
    func getCurrentMetrics() async -> AdaptiveLearningMetrics {
        updateLearningMetrics()
        return learningMetrics ?? AdaptiveLearningMetrics.default
    }
    
    private func updateLearningMetrics() {
        let effectiveness = calculateSessionEffectiveness()
        let cognitiveLoad = assessCognitiveLoad()
        let learningState = determineLearningState()
        
        learningMetrics = AdaptiveLearningMetrics(
            sessionEffectiveness: effectiveness,
            cognitiveLoadLevel: cognitiveLoad,
            learningState: learningState,
            attentionLevel: Double.random(in: 0.6...0.95),
            engagementScore: Double.random(in: 0.7...0.95),
            comprehensionRate: Double.random(in: 0.65...0.9),
            retentionPrediction: Double.random(in: 0.7...0.95),
            isActive: isSessionActive,
            timestamp: Date()
        )
    }
    
    private func calculateSessionEffectiveness() -> Double {
        guard let session = currentSession else { return 0.5 }
        
        let sessionDuration = Date().timeIntervalSince(session.startTime)
        let optimalDuration: TimeInterval = 15 * 60 // 15 minutes
        
        // Peak effectiveness around 15 minutes, declining after
        if sessionDuration <= optimalDuration {
            return min(0.95, 0.6 + (sessionDuration / optimalDuration) * 0.35)
        } else {
            let overtime = sessionDuration - optimalDuration
            let decayFactor = min(0.3, overtime / (10 * 60)) // Decay over 10 minutes
            return max(0.4, 0.95 - decayFactor)
        }
    }
    
    private func assessCognitiveLoad() -> CognitiveLoadLevel {
        let randomLoad = Double.random(in: 0...1)
        switch randomLoad {
        case 0.8...1.0: return .overload
        case 0.6...0.8: return .high
        case 0.3...0.6: return .optimal
        default: return .low
        }
    }
    
    private func determineLearningState() -> LearningState {
        guard isSessionActive else { return .paused }
        
        let states: [LearningState] = [.focused, .engaged, .struggling, .mastering]
        return states.randomElement() ?? .focused
    }
}

// MARK: - Supporting Models

struct AdaptiveSession {
    let id: UUID
    let startTime: Date
    let learnerID: String
    let language: Language
    var endTime: Date?
    var completedExercises: Int = 0
    var correctAnswers: Int = 0
    var totalAnswers: Int = 0
}

struct AdaptiveLearningMetrics {
    let sessionEffectiveness: Double
    let cognitiveLoadLevel: CognitiveLoadLevel
    let learningState: LearningState
    let attentionLevel: Double
    let engagementScore: Double
    let comprehensionRate: Double
    let retentionPrediction: Double
    let isActive: Bool
    let timestamp: Date
    
    static let `default` = AdaptiveLearningMetrics(
        sessionEffectiveness: 0.75,
        cognitiveLoadLevel: .optimal,
        learningState: .focused,
        attentionLevel: 0.8,
        engagementScore: 0.8,
        comprehensionRate: 0.75,
        retentionPrediction: 0.8,
        isActive: false,
        timestamp: Date()
    )
}

enum CognitiveLoadLevel {
    case low, optimal, high, overload
    
    var color: Color {
        switch self {
        case .low: return .blue
        case .optimal: return .green
        case .high: return .orange
        case .overload: return .red
        }
    }
}

enum LearningState {
    case focused, engaged, struggling, mastering, paused
    
    var displayName: String {
        switch self {
        case .focused: return "Focused"
        case .engaged: return "Engaged"
        case .struggling: return "Needs Help"
        case .mastering: return "Mastering"
        case .paused: return "Paused"
        }
    }
    
    var icon: String {
        switch self {
        case .focused: return "eye"
        case .engaged: return "hand.raised.fill"
        case .struggling: return "questionmark.circle"
        case .mastering: return "star.fill"
        case .paused: return "pause.circle"
        }
    }
} 