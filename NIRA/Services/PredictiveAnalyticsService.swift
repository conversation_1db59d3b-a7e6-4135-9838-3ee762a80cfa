import Foundation
import SwiftUI
import Combine

// Import DashboardInsight from LearningAnalyticsDashboardService
// Note: This creates a dependency that should be managed through proper architecture

@MainActor
class PredictiveAnalyticsService: ObservableObject {
    static let shared = PredictiveAnalyticsService()
    
    @Published var performanceForecast: PerformanceForecast?
    @Published var learningInsights: [DashboardInsight] = []
    @Published var riskAssessment: RiskAssessment?
    @Published var progressPredictions: [ProgressPrediction] = []
    @Published var isAnalyzing: Bool = false
    
    private let userPreferencesService = UserPreferencesService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private let curriculumService = CurriculumService.shared
    private let aiPersonalizationService = AIPersonalizationService.shared
    
    private var cancellables = Set<AnyCancellable>()
    private var historicalData: [AnalyticsDataPoint] = []
    
    private init() {
        setupSubscriptions()
        initializePredictiveModels()
    }
    
    // MARK: - Public Methods
    
    func generatePredictiveInsights() async {
        isAnalyzing = true
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.generatePerformanceForecast() }
            group.addTask { await self.analyzeLearningPatterns() }
            group.addTask { await self.assessLearningRisk() }
            group.addTask { await self.predictProgressTrajectory() }
        }
        
        await MainActor.run {
            self.isAnalyzing = false
        }
    }
    
    func getOptimalStudyTime() -> OptimalStudyTime {
        let historicalPerformance = analyzeTimeBasedPerformance()
        let currentStreak = 7 // Placeholder - would get from analytics service
        let energyLevels = predictEnergyLevels()
        
        return OptimalStudyTime(
            recommendedStartTime: findOptimalStartTime(from: historicalPerformance),
            optimalDuration: calculateOptimalDuration(streak: currentStreak),
            energyForecast: energyLevels,
            confidenceScore: calculateTimeConfidence(historicalPerformance),
            reasoning: generateTimeRecommendationReason(historicalPerformance, energyLevels)
        )
    }
    
    func predictLearningOutcome(for lesson: CurriculumLesson) -> LearningOutcomePrediction {
        let userSkillLevel = curriculumService.userSkillLevels[lesson.skillArea] ?? .beginner
        let historicalAccuracy = getHistoricalAccuracy(for: lesson.skillArea)
        let difficultyGap = abs(lesson.difficulty.rawValue - userSkillLevel.rawValue)
        
        let successProbability = calculateSuccessProbability(
            skillLevel: userSkillLevel,
            lessonDifficulty: lesson.difficulty,
            historicalAccuracy: historicalAccuracy,
            difficultyGap: difficultyGap
        )
        
        let estimatedTime = predictCompletionTime(
            baseDuration: lesson.estimatedDuration,
            difficultyGap: difficultyGap,
            userPacing: aiPersonalizationService.learningStyleProfile?.preferredPacing ?? .medium
        )
        
        return LearningOutcomePrediction(
            lessonId: lesson.id,
            successProbability: successProbability,
            estimatedCompletionTime: estimatedTime,
            expectedAccuracy: predictAccuracy(lesson: lesson),
            strugglingAreas: identifyPotentialStruggles(lesson: lesson),
            recommendedPreparation: generatePreparationRecommendations(lesson: lesson)
        )
    }
    
    func getMotivationForecast() -> MotivationForecast {
        let recentEngagement = analyzeRecentEngagement()
        let streakImpact = analyzeStreakImpact()
        let achievementProximity = analyzeAchievementProximity()
        
        let motivationScore = calculateMotivationScore(
            engagement: recentEngagement,
            streak: streakImpact,
            achievements: achievementProximity
        )
        
        return MotivationForecast(
            currentMotivationLevel: motivationScore,
            trendDirection: predictMotivationTrend(recentEngagement),
            riskFactors: identifyMotivationRisks(),
            boostOpportunities: identifyMotivationBoosters(),
            recommendedActions: generateMotivationActions(motivationScore)
        )
    }
    
    // MARK: - Private Methods
    
    private func setupSubscriptions() {
        // Listen for new analytics data
        analyticsService.$userProgress
            .debounce(for: .seconds(5), scheduler: RunLoop.main)
            .sink { [weak self] progress in
                self?.updateHistoricalData(from: progress)
                Task {
                    await self?.generatePredictiveInsights()
                }
            }
            .store(in: &cancellables)
    }
    
    private func initializePredictiveModels() {
        Task {
            await generatePredictiveInsights()
        }
    }
    
    private func generatePerformanceForecast() async {
        let recentPerformance = getRecentPerformanceData()
        let trendAnalysis = analyzeTrends(recentPerformance)
        
        var skillForecasts: [SkillArea: SkillForecast] = [:]
        
        for skillArea in SkillArea.allCases {
            let skillData = recentPerformance.filter { $0.skillArea == skillArea }
            let forecast = generateSkillForecast(skillArea: skillArea, data: skillData)
            skillForecasts[skillArea] = forecast
        }
        
        let overallForecast = PerformanceForecast(
            timeframe: .thirtyDays,
            skillForecasts: skillForecasts,
            overallTrend: trendAnalysis.overallTrend,
            confidenceLevel: trendAnalysis.confidence,
            keyInsights: generateForecastInsights(skillForecasts),
            generatedAt: Date()
        )
        
        await MainActor.run {
            self.performanceForecast = overallForecast
        }
    }
    
    private func analyzeLearningPatterns() async {
        let patterns = identifyLearningPatterns()
        let insights = patterns.map { pattern in
            DashboardInsight(
                id: UUID(),
                title: generateInsightTitle(pattern),
                description: pattern.description,
                type: mapPatternToInsightType(pattern.type),
                confidence: pattern.confidence,
                actionable: true,
                recommendation: generatePatternRecommendations(pattern).first ?? "Continue current approach"
            )
        }
        
        await MainActor.run {
            self.learningInsights = insights
        }
    }
    
    private func assessLearningRisk() async {
        let engagementTrend = analyzeEngagementTrend()
        let performanceDecline = analyzePerformanceDecline()
        let streakVulnerability = analyzeStreakVulnerability()
        
        let riskLevel = calculateOverallRisk(
            engagement: engagementTrend,
            performance: performanceDecline,
            streak: streakVulnerability
        )
        
        let assessment = RiskAssessment(
            overallRiskLevel: riskLevel,
            engagementRisk: engagementTrend,
            performanceRisk: performanceDecline,
            streakRisk: streakVulnerability,
            interventionRecommendations: generateInterventionRecommendations(riskLevel),
            assessmentDate: Date()
        )
        
        await MainActor.run {
            self.riskAssessment = assessment
        }
    }
    
    private func predictProgressTrajectory() async {
        let currentProgress = curriculumService.getLanguageOverallProgress()
        let learningVelocity = calculateLearningVelocity()
        
        var predictions: [ProgressPrediction] = []
        
        // Predict progress for different timeframes
        for timeframe in [7, 30, 90] {
            let prediction = ProgressPrediction(
                timeframeDays: timeframe,
                predictedLessonsCompleted: predictLessonsCompleted(
                    current: currentProgress.completedLessons,
                    velocity: learningVelocity,
                    days: timeframe
                ),
                predictedSkillLevels: predictSkillLevels(timeframe: timeframe),
                confidenceInterval: calculateConfidenceInterval(timeframe: timeframe),
                milestonesPredicted: predictMilestones(timeframe: timeframe),
                generatedAt: Date()
            )
            predictions.append(prediction)
        }
        
        await MainActor.run {
            self.progressPredictions = predictions
        }
    }
    
    // MARK: - Helper Methods
    
    private func updateHistoricalData(from progress: [UUID: AdaptiveUserProgress]) {
        let newDataPoints = progress.values.map { progressItem in
            AnalyticsDataPoint(
                timestamp: progressItem.completedAt ?? Date(),
                skillArea: .vocabulary, // This would need to be determined from the lesson
                accuracy: progressItem.accuracyScore ?? 0.0,
                timeSpent: progressItem.timeSpentSeconds,
                difficulty: .beginner, // This would need to be determined from the lesson
                engagementLevel: calculateEngagementLevel(progressItem)
            )
        }
        
        historicalData.append(contentsOf: newDataPoints)
        
        // Keep only recent data (last 90 days)
        let cutoffDate = Date().addingTimeInterval(-90 * 24 * 60 * 60)
        historicalData = historicalData.filter { $0.timestamp > cutoffDate }
    }
    
    private func calculateEngagementLevel(_ progress: AdaptiveUserProgress) -> Double {
        // Calculate engagement based on completion status and time spent
        let baseEngagement = progress.completionStatus == .completed ? 0.8 : 0.4
        let timeBonus = min(0.2, Double(progress.timeSpentSeconds) / 600.0) // Bonus for time spent
        return min(1.0, baseEngagement + timeBonus)
    }
    
    private func getRecentPerformanceData() -> [AnalyticsDataPoint] {
        let thirtyDaysAgo = Date().addingTimeInterval(-30 * 24 * 60 * 60)
        return historicalData.filter { $0.timestamp > thirtyDaysAgo }
    }
    
    private func analyzeTrends(_ data: [AnalyticsDataPoint]) -> TrendAnalysis {
        guard data.count >= 5 else {
            return TrendAnalysis(overallTrend: .stable, confidence: 0.1)
        }
        
        let sortedData = data.sorted { $0.timestamp < $1.timestamp }
        let accuracyTrend = calculateTrend(sortedData.map { $0.accuracy })
        let engagementTrend = calculateTrend(sortedData.map { $0.engagementLevel })
        
        let overallTrend: PredictiveTrendDirection
        if accuracyTrend > 0.1 && engagementTrend > 0.1 {
            overallTrend = .improving
        } else if accuracyTrend < -0.1 || engagementTrend < -0.1 {
            overallTrend = .declining
        } else {
            overallTrend = .stable
        }
        
        return TrendAnalysis(
            overallTrend: overallTrend,
            confidence: min(1.0, Double(data.count) / 30.0)
        )
    }
    
    private func calculateTrend(_ values: [Double]) -> Double {
        guard values.count >= 2 else { return 0.0 }
        
        let firstHalf = values.prefix(values.count / 2)
        let secondHalf = values.suffix(values.count / 2)
        
        let firstAvg = firstHalf.reduce(0, +) / Double(firstHalf.count)
        let secondAvg = secondHalf.reduce(0, +) / Double(secondHalf.count)
        
        return secondAvg - firstAvg
    }
    
    // Additional helper methods would be implemented here...
    // For brevity, I'm including key method signatures
    
    private func generateSkillForecast(skillArea: SkillArea, data: [AnalyticsDataPoint]) -> SkillForecast {
        // Implementation for skill-specific forecasting
        return SkillForecast(
            skillArea: skillArea,
            currentLevel: curriculumService.userSkillLevels[skillArea] ?? .beginner,
            predictedLevel: .intermediate, // Calculated prediction
            improvementRate: 0.1,
            timeToNextLevel: 14
        )
    }
    
    private func identifyLearningPatterns() -> [DetectedPattern] {
        // Implementation for pattern detection
        return []
    }
    
    private func calculateLearningVelocity() -> Double {
        // Calculate lessons completed per day
        let recentData = getRecentPerformanceData()
        let startDate = recentData.first?.timestamp ?? Date()
        let endDate = Date()
        let daysSpan = max(1, endDate.timeIntervalSince(startDate)) / 86400
        return Double(recentData.count) / daysSpan
    }
    
    // MARK: - Missing Method Implementations (Stubs)
    
    private func analyzeTimeBasedPerformance() -> [AnalyticsDataPoint] {
        return getRecentPerformanceData()
    }
    
    private func predictEnergyLevels() -> EnergyForecast {
        return EnergyForecast(
            morningEnergy: 0.8,
            afternoonEnergy: 0.6,
            eveningEnergy: 0.7,
            optimalTimeSlot: .morning
        )
    }
    
    private func findOptimalStartTime(from data: [AnalyticsDataPoint]) -> Date {
        // Default to 9 AM
        let calendar = Calendar.current
        return calendar.date(bySettingHour: 9, minute: 0, second: 0, of: Date()) ?? Date()
    }
    
    private func calculateOptimalDuration(streak: Int) -> Int {
        // Base duration of 30 minutes, adjusted by streak
        return max(15, min(60, 30 + streak))
    }
    
    private func calculateTimeConfidence(_ data: [AnalyticsDataPoint]) -> Double {
        return min(1.0, Double(data.count) / 30.0)
    }
    
    private func generateTimeRecommendationReason(_ performance: [AnalyticsDataPoint], _ energy: EnergyForecast) -> String {
        return "Based on your learning patterns, \(energy.optimalTimeSlot.displayName.lowercased()) is your optimal study time."
    }
    
    private func getHistoricalAccuracy(for skillArea: SkillArea) -> Double {
        let skillData = historicalData.filter { $0.skillArea == skillArea }
        guard !skillData.isEmpty else { return 0.7 }
        return skillData.map { $0.accuracy }.reduce(0, +) / Double(skillData.count)
    }
    
    private func calculateSuccessProbability(skillLevel: SkillLevel, lessonDifficulty: SkillLevel, historicalAccuracy: Double, difficultyGap: Int) -> Double {
        let baseSuccess = historicalAccuracy
        let difficultyPenalty = Double(difficultyGap) * 0.1
        return max(0.1, min(1.0, baseSuccess - difficultyPenalty))
    }
    
    private func predictCompletionTime(baseDuration: Int, difficultyGap: Int, userPacing: LearningPacing) -> Int {
        let pacingMultiplier: Double
        switch userPacing {
        case .slow: pacingMultiplier = 1.5
        case .medium: pacingMultiplier = 1.0
        case .fast: pacingMultiplier = 0.8
        }
        
        let difficultyMultiplier = 1.0 + (Double(difficultyGap) * 0.2)
        return Int(Double(baseDuration) * pacingMultiplier * difficultyMultiplier)
    }
    
    private func predictAccuracy(lesson: CurriculumLesson) -> Double {
        return getHistoricalAccuracy(for: lesson.skillArea)
    }
    
    private func identifyPotentialStruggles(lesson: CurriculumLesson) -> [String] {
        // Placeholder implementation
        return ["Complex grammar structures", "New vocabulary"]
    }
    
    private func generatePreparationRecommendations(lesson: CurriculumLesson) -> [String] {
        return ["Review previous lessons", "Practice vocabulary", "Warm up with easier exercises"]
    }
    
    private func analyzeRecentEngagement() -> Double {
        let recentData = getRecentPerformanceData()
        guard !recentData.isEmpty else { return 0.5 }
        return recentData.map { $0.engagementLevel }.reduce(0, +) / Double(recentData.count)
    }
    
    private func analyzeStreakImpact() -> Double {
        // Placeholder - would analyze how streak affects performance
        return 0.7
    }
    
    private func analyzeAchievementProximity() -> Double {
        // Placeholder - would analyze how close user is to achievements
        return 0.6
    }
    
    private func calculateMotivationScore(engagement: Double, streak: Double, achievements: Double) -> Double {
        return (engagement + streak + achievements) / 3.0
    }
    
    private func predictMotivationTrend(_ engagement: Double) -> PredictiveTrendDirection {
        if engagement > 0.7 { return .improving }
        else if engagement < 0.4 { return .declining }
        else { return .stable }
    }
    
    private func identifyMotivationRisks() -> [MotivationRisk] {
        return [
            MotivationRisk(
                factor: "Low engagement",
                severity: .medium,
                description: "Recent engagement levels are below average"
            )
        ]
    }
    
    private func identifyMotivationBoosters() -> [MotivationBooster] {
        return [
            MotivationBooster(
                opportunity: "Achievement unlock",
                impact: 0.8,
                description: "You're close to unlocking a new achievement"
            )
        ]
    }
    
    private func generateMotivationActions(_ score: Double) -> [MotivationAction] {
        return [
            MotivationAction(
                title: "Take a short break",
                description: "A 5-minute break can help refresh your focus",
                expectedImpact: 0.3,
                timeToImplement: 5
            )
        ]
    }
    
    private func generateForecastInsights(_ forecasts: [SkillArea: SkillForecast]) -> [String] {
        return ["Your vocabulary skills are improving rapidly", "Consider focusing more on grammar"]
    }
    
    private func mapPatternToInsightType(_ type: PatternType) -> InsightType {
        switch type {
        case .peakPerformance: return .strength
        case .learningPlateau: return .weakness
        case .rapidImprovement: return .opportunity
        case .consistentProgress: return .pattern
        case .irregularStudy: return .weakness
        }
    }
    
    private func generateInsightTitle(_ pattern: DetectedPattern) -> String {
        return "Learning Pattern Detected"
    }
    
    private func generatePatternRecommendations(_ pattern: DetectedPattern) -> [String] {
        return ["Adjust study schedule", "Try different content types"]
    }
    
    private func analyzeEngagementTrend() -> RiskLevel {
        let engagement = analyzeRecentEngagement()
        if engagement > 0.7 { return .low }
        else if engagement > 0.4 { return .medium }
        else { return .high }
    }
    
    private func analyzePerformanceDecline() -> RiskLevel {
        // Placeholder implementation
        return .low
    }
    
    private func analyzeStreakVulnerability() -> RiskLevel {
        // Placeholder implementation
        return .medium
    }
    
    private func calculateOverallRisk(engagement: RiskLevel, performance: RiskLevel, streak: RiskLevel) -> RiskLevel {
        let risks = [engagement, performance, streak]
        if risks.contains(.critical) { return .critical }
        else if risks.contains(.high) { return .high }
        else if risks.contains(.medium) { return .medium }
        else { return .low }
    }
    
    private func generateInterventionRecommendations(_ risk: RiskLevel) -> [InterventionRecommendation] {
        return [
            InterventionRecommendation(
                title: "Adjust difficulty",
                description: "Consider easier lessons to rebuild confidence",
                urgency: risk,
                estimatedImpact: 0.7,
                actionSteps: ["Review current level", "Select easier content", "Monitor progress"]
            )
        ]
    }
    
    private func predictLessonsCompleted(current: Int, velocity: Double, days: Int) -> Int {
        return current + Int(velocity * Double(days))
    }
    
    private func predictSkillLevels(timeframe: Int) -> [SkillArea: SkillLevel] {
        var predictions: [SkillArea: SkillLevel] = [:]
        for skillArea in SkillArea.allCases {
            predictions[skillArea] = curriculumService.userSkillLevels[skillArea] ?? .beginner
        }
        return predictions
    }
    
    private func calculateConfidenceInterval(timeframe: Int) -> ConfidenceInterval {
        let confidence = max(0.5, min(0.95, 1.0 - Double(timeframe) / 100.0))
        return ConfidenceInterval(lower: 0.8, upper: 1.2, confidence: confidence)
    }
    
    private func predictMilestones(timeframe: Int) -> [PredictedMilestone] {
        return [
            PredictedMilestone(
                title: "Complete 10 lessons",
                estimatedDate: Date().addingTimeInterval(TimeInterval(timeframe * 86400 / 2)),
                probability: 0.8,
                requirements: ["Maintain current pace", "Complete daily goals"]
            )
        ]
    }
}

// MARK: - Supporting Models

struct PerformanceForecast {
    let timeframe: ForecastTimeframe
    let skillForecasts: [SkillArea: SkillForecast]
    let overallTrend: PredictiveTrendDirection
    let confidenceLevel: Double
    let keyInsights: [String]
    let generatedAt: Date
}

struct SkillForecast {
    let skillArea: SkillArea
    let currentLevel: SkillLevel
    let predictedLevel: SkillLevel
    let improvementRate: Double
    let timeToNextLevel: Int // days
}

enum ForecastTimeframe {
    case sevenDays
    case thirtyDays
    case ninetyDays
    
    var displayName: String {
        switch self {
        case .sevenDays: return "7 Days"
        case .thirtyDays: return "30 Days"
        case .ninetyDays: return "90 Days"
        }
    }
}

enum PredictiveTrendDirection {
    case improving
    case stable
    case declining
    
    var displayName: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Declining"
        }
    }
    
    var color: Color {
        switch self {
        case .improving: return .green
        case .stable: return .blue
        case .declining: return .red
        }
    }
}

// Note: DashboardInsight and InsightType are defined in LearningAnalyticsDashboardService.swift
// LearningInsight is defined in SimulationService.swift for simulation-specific insights

struct RiskAssessment {
    let overallRiskLevel: RiskLevel
    let engagementRisk: RiskLevel
    let performanceRisk: RiskLevel
    let streakRisk: RiskLevel
    let interventionRecommendations: [InterventionRecommendation]
    let assessmentDate: Date
}

enum RiskLevel {
    case low
    case medium
    case high
    case critical
    
    var displayName: String {
        switch self {
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        case .critical: return "Critical Risk"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

struct InterventionRecommendation {
    let title: String
    let description: String
    let urgency: RiskLevel
    let estimatedImpact: Double
    let actionSteps: [String]
}

struct ProgressPrediction {
    let timeframeDays: Int
    let predictedLessonsCompleted: Int
    let predictedSkillLevels: [SkillArea: SkillLevel]
    let confidenceInterval: ConfidenceInterval
    let milestonesPredicted: [PredictedMilestone]
    let generatedAt: Date
}

struct ConfidenceInterval {
    let lower: Double
    let upper: Double
    let confidence: Double // e.g., 0.95 for 95% confidence
}

struct PredictedMilestone {
    let title: String
    let estimatedDate: Date
    let probability: Double
    let requirements: [String]
}

struct OptimalStudyTime {
    let recommendedStartTime: Date
    let optimalDuration: Int // minutes
    let energyForecast: EnergyForecast
    let confidenceScore: Double
    let reasoning: String
}

struct EnergyForecast {
    let morningEnergy: Double
    let afternoonEnergy: Double
    let eveningEnergy: Double
    let optimalTimeSlot: PredictiveTimeSlot
}

enum PredictiveTimeSlot {
    case morning
    case afternoon
    case evening
    case night
    
    var displayName: String {
        switch self {
        case .morning: return "Morning"
        case .afternoon: return "Afternoon"
        case .evening: return "Evening"
        case .night: return "Night"
        }
    }
}

struct LearningOutcomePrediction {
    let lessonId: UUID
    let successProbability: Double
    let estimatedCompletionTime: Int
    let expectedAccuracy: Double
    let strugglingAreas: [String]
    let recommendedPreparation: [String]
}

struct MotivationForecast {
    let currentMotivationLevel: Double
    let trendDirection: PredictiveTrendDirection
    let riskFactors: [MotivationRisk]
    let boostOpportunities: [MotivationBooster]
    let recommendedActions: [MotivationAction]
}

struct MotivationRisk {
    let factor: String
    let severity: RiskLevel
    let description: String
}

struct MotivationBooster {
    let opportunity: String
    let impact: Double
    let description: String
}

struct MotivationAction {
    let title: String
    let description: String
    let expectedImpact: Double
    let timeToImplement: Int // minutes
}

struct AnalyticsDataPoint {
    let timestamp: Date
    let skillArea: SkillArea
    let accuracy: Double
    let timeSpent: Int
    let difficulty: SkillLevel
    let engagementLevel: Double
}

struct TrendAnalysis {
    let overallTrend: PredictiveTrendDirection
    let confidence: Double
}

struct DetectedPattern {
    let type: PatternType
    let description: String
    let confidence: Double
    let impact: PatternImpact
    let detectedAt: Date
}

