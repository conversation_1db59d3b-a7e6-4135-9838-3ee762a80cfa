<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>NSMicrophoneUsageDescription</key>
	<string>NIRA needs access to your microphone for live voice conversations with AI language tutors. This enables real-time speaking practice and pronunciation feedback.</string>
	<key>NSCameraUsageDescription</key>
	<string>NIRA needs access to your camera to capture images for language learning discussions with your AI tutors.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>NIRA needs access to your photo library to share images with your AI language tutors for learning conversations.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>NIRA uses speech recognition to provide live transcription during voice conversations with AI tutors, helping you see what you're saying in real-time.</string>

	<!-- HealthKit Privacy Descriptions for Biometric Learning -->
	<key>NSHealthShareUsageDescription</key>
	<string>NIRA uses your health data to optimize your learning experience by monitoring stress levels and cognitive load during lessons. This helps personalize content difficulty and timing for better learning outcomes.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>NIRA may record learning session data to your Health app to track your educational activity and learning progress over time.</string>

	<!-- Additional Privacy Descriptions for Compliance -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>NIRA uses your location to provide culturally relevant content and local language variations for your learning experience.</string>
	<key>NSContactsUsageDescription</key>
	<string>NIRA can access your contacts to help you practice conversations with people you know, with your permission.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>NIRA can integrate with your calendar to schedule study sessions and track your learning progress.</string>
	<key>NSRemindersUsageDescription</key>
	<string>NIRA can create reminders to help you maintain your learning streak and practice regularly.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>NIRA uses tracking to provide personalized learning experiences and measure the effectiveness of our educational content. Your privacy is important to us.</string>

	<!-- App Transport Security Configuration -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>supabase.co</key>
			<dict>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>googleapis.com</key>
			<dict>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
		</dict>
	</dict>

	<!-- Security and Privacy Configuration -->
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSSupportsAutomaticTermination</key>
	<true/>
	<key>NSSupportsSuddenTermination</key>
	<true/>
</dict>
</plist>
