//
//  ContentView.swift
//  NIRA
//
//  Created by MAGESH DHANASEKARAN on 5/22/25.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var authService = AuthenticationService.shared
    @State private var selectedTab = 0
    @State private var showOnboarding = false

    var body: some View {
        Group {
            if showOnboarding {
                OnboardingView {
                    showOnboarding = false
                }
            } else if !authService.isAuthenticated {
                // Temporarily bypass authentication for testing
                MainTabView(selectedTab: $selectedTab)
                    .onAppear {
                        // Auto-enable guest mode for testing
                        Task {
                            await authService.continueAsGuest()
                        }
                    }
            } else {
                MainTabView(selectedTab: $selectedTab)
            }
        }
        .onAppear {
            checkOnboardingStatus()
        }
        .alert("Authentication Error", isPresented: .constant(authService.errorMessage != nil)) {
            Button("OK") {
                authService.errorMessage = nil
            }
        } message: {
            if let errorMessage = authService.errorMessage {
                Text(errorMessage)
            }
        }
    }

    private func checkOnboardingStatus() {
        // Temporarily skip onboarding for testing
        showOnboarding = false
        // let hasCompletedOnboarding = UserDefaults.standard.bool(forKey: "hasCompletedOnboarding")
        // showOnboarding = !hasCompletedOnboarding
    }
}

// MARK: - Color Extensions moved to Config/ColorExtensions.swift

// MARK: - Main Tab View

struct MainTabView: View {
    @Binding var selectedTab: Int
    @State private var currentStreak = 7
    @State private var userName = "Alex"
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        ZStack {
            // Dynamic background that changes with time of day
            dynamicBackground
                .ignoresSafeArea()

            // Universal 5-tab navigation for all 50 languages
            universalNavigationView
        }
    }
    
    // MARK: - Universal Navigation for All 50 Languages
    
    private var universalNavigationView: some View {
        TabView(selection: $selectedTab) {
            // Tab 0: Dashboard (Universal)
            HomeView(selectedTab: $selectedTab)
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 0 ? "house.fill" : "house",
                        title: "Dashboard",
                        isSelected: selectedTab == 0,
                        color: .emojiOrange
                    )
                }
                .tag(0)

            // Tab 1: Learn with Lessons/Discover tabs
            EnhancedLessonsView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 1 ? "book.fill" : "book",
                        title: "Learn",
                        isSelected: selectedTab == 1,
                        color: .emojiBlue
                    )
                }
                .tag(1)

            // Tab 2: Practice (Adaptive: Read/Write for scripts, Speaking/Listening for others)
            PracticeView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 2 ? "target" : "scope",
                        title: "Practice",
                        isSelected: selectedTab == 2,
                        color: .emojiGreen
                    )
                }
                .tag(2)

            // Tab 3: Live Assist (Restored Agents)
            AgentsView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 3 ? "person.3.fill" : "person.3",
                        title: "Live Assist",
                        isSelected: selectedTab == 3,
                        color: .emojiPurple
                    )
                }
                .tag(3)

            // Tab 4: More (All existing features)
            MoreView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 4 ? "ellipsis.circle.fill" : "ellipsis.circle",
                        title: "More",
                        isSelected: selectedTab == 4,
                        color: .emojiPink
                    )
                }
                .tag(4)
        }
        .accentColor(Color("NiraPrimary"))
        .onAppear {
            configureTabBarAppearance()
        }
    }

    private var dynamicBackground: some View {
        let hour = Calendar.current.component(.hour, from: Date())
        let gradientColors: [Color]

        switch hour {
        case 5..<12:  // Morning
            gradientColors = Color.swissGradient
        case 12..<17: // Afternoon
            gradientColors = Color.mediterraneanGradient
        case 17..<20: // Evening
            gradientColors = Color.tuscanGradient
        default:      // Night
            gradientColors = Color.alpineGradient
        }

        // Optimized: Use single color instead of gradient for better performance
        if colorScheme == .dark {
            return Color.black
        } else {
            return Color(gradientColors.first ?? .clear)
                .opacity(0.05)
        }
    }

    private func configureTabBarAppearance() {
        let appearance = UITabBarAppearance()

        if colorScheme == .dark {
            // Dark mode configuration
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = UIColor.black
            appearance.shadowColor = UIColor.clear
        } else {
            // Light mode configuration
            appearance.configureWithDefaultBackground()
            appearance.backgroundColor = UIColor.systemBackground
        }

        // Apply the appearance
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Enhanced Tab Item View for Gen Z/Alpha Appeal

struct TabItemView: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let color: Color
    @State private var bounceAnimation = false

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: isSelected ? .bold : .medium))
                .foregroundColor(isSelected ? color : .secondary)
                .scaleEffect(isSelected && bounceAnimation ? 1.2 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: bounceAnimation)

            Text(title)
                .font(.caption2)
                .fontWeight(isSelected ? .bold : .medium)
                .foregroundColor(isSelected ? color : .secondary)
        }
        .onChange(of: isSelected) { oldValue, newValue in
            if newValue {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    bounceAnimation = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    bounceAnimation = false
                }
            }
        }
    }
}

// MARK: - Home View

// MARK: - Connected Dashboard Components

struct ConnectedDailyGoalView: View {
    let goalData: TodaysGoalData
    @Binding var particleAnimation: Bool
    let onGoalTap: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Today's Goal 🎯")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primaryText)

                Spacer()

                Text("\(goalData.dailyCompleted)/\(goalData.dailyGoal) lessons")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraAccent)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.niraAccent.opacity(0.2))
                    .cornerRadius(12)
            }

            // Progress bar
            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 12)

                RoundedRectangle(cornerRadius: 10)
                    .fill(
                        LinearGradient(
                            colors: [.niraSuccess, .niraSecondary],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: max(0, CGFloat(goalData.dailyProgress) * UIScreen.main.bounds.width * 0.8), height: 12)
                    .animation(.easeInOut(duration: 1.5), value: goalData.dailyProgress)
            }

            // Language-specific progress
            HStack {
                Text("\(goalData.selectedLanguage.flag) \(goalData.selectedLanguage.displayName)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(goalData.languageCompleted)/\(goalData.languageTotal) total")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            if goalData.isDailyGoalComplete {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.niraSuccess)
                        .font(.title2)

                    Text("Goal achieved! You're on fire! 🔥")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.niraSuccess)
                }
                .padding()
                .background(Color.niraSuccess.opacity(0.2))
                .cornerRadius(12)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(20)
        .shadow(color: .niraSuccess.opacity(0.3), radius: 8, x: 0, y: 4)
        .onTapGesture {
            onGoalTap()
        }
    }
}

struct ConnectedLanguageSelectionView: View {
    @Binding var selectedLanguage: Language
    let onLanguageSelected: (Language) -> Void
    @State private var showingFullSelection = false

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Choose Your Adventure 🚀")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primaryText)

                Spacer()

                Button("View All 50") {
                    showingFullSelection = true
                }
                .font(.subheadline)
                .foregroundColor(.niraPrimary)
            }

            // Popular languages quick access
            PopularLanguagesQuickAccess(
                selectedLanguage: $selectedLanguage,
                onLanguageSelected: onLanguageSelected
            )
        }
        .sheet(isPresented: $showingFullSelection) {
            NavigationView {
                ModernLanguageSelectionView(
                    selectedLanguage: $selectedLanguage,
                    onLanguageSelected: { language in
                        onLanguageSelected(language)
                        showingFullSelection = false
                    }
                )
                .navigationTitle("Select Language")
                .navigationBarTitleDisplayMode(.large)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            showingFullSelection = false
                        }
                    }
                }
            }
        }
    }
}

struct ConnectedLanguageCardView: View {
    let language: Language
    let isSelected: Bool
    let onSelect: () -> Void
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared

    var body: some View {
        let progress = dashboardCoordinator.getLanguageProgress(for: language)

        VStack(spacing: 12) {
            Text(language.flag)
                .font(.system(size: 40))

            VStack(spacing: 4) {
                Text(language.displayName)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(isSelected ? .white : .primaryText)

                Text(progress.progressText)
                    .font(.caption)
                    .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)

                Text(progress.percentageText)
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(isSelected ? .white : .niraAccent)
            }
        }
        .frame(width: 120, height: 140)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(isSelected ? Color("NiraPrimary") : Color.cardBackground)
                .shadow(color: isSelected ? Color("NiraPrimary").opacity(0.4) : .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3), value: isSelected)
        .onTapGesture {
            onSelect()
        }
    }
}

enum QuickActionType {
    case startLesson
    case practiceSpeak
    case culturalSim
    case reviewWords
}

struct ConnectedQuickActionsView: View {
    let selectedLanguage: Language
    let onActionSelected: (QuickActionType) -> Void

    private let actions: [(String, String, QuickActionType)] = [
        ("Start Lesson", "book.fill", .startLesson),
        ("Practice Speaking", "mic.fill", .practiceSpeak),
        ("Cultural Sim", "globe", .culturalSim),
        ("Review Words", "repeat", .reviewWords)
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions ⚡")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(Array(actions.enumerated()), id: \.offset) { index, action in
                    ConnectedQuickActionCard(
                        title: action.0,
                        icon: action.1,
                        language: selectedLanguage,
                        onTap: { onActionSelected(action.2) }
                    )
                }
            }
        }
    }
}

struct ConnectedQuickActionCard: View {
    let title: String
    let icon: String
    let language: Language
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.white)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.niraPrimary.gradient)
                )

            VStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Text("for \(language.displayName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(height: 100)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .niraPrimary.opacity(0.2), radius: 4, x: 0, y: 2)
        )
        .onTapGesture {
            onTap()
        }
    }
}

struct ConnectedRecommendationsView: View {
    let recommendations: [DashboardRecommendation]
    let language: Language

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recommended for You 💡")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            if recommendations.isEmpty {
                EmptyRecommendationsCard()
            } else {
                VStack(spacing: 12) {
                    ForEach(recommendations.prefix(3)) { recommendation in
                        RecommendationCard(recommendation: recommendation)
                    }
                }
            }
        }
    }
}

struct RecommendationCard: View {
    let recommendation: DashboardRecommendation

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: recommendation.type.icon)
                .font(.title2)
                .foregroundColor(recommendation.type.color)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(recommendation.type.color.opacity(0.2))
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(recommendation.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                Text("\(recommendation.estimatedDuration) min")
                    .font(.caption2)
                    .foregroundColor(.niraAccent)
            }

            Spacer()

            VStack(spacing: 4) {
                Text("\(Int(recommendation.confidenceScore * 100))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraPrimary)

                Text("match")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.cardBackground)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
}

struct EmptyRecommendationsCard: View {
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "brain.head.profile")
                .font(.title2)
                .foregroundColor(.gray)

            VStack(alignment: .leading, spacing: 4) {
                Text("AI is learning about you")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Text("Complete more lessons to get personalized recommendations")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct ConnectedRecentActivityView: View {
    let activities: [DashboardActivity]

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Activity 📈")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            if activities.isEmpty {
                EmptyActivityCard()
            } else {
                VStack(spacing: 12) {
                    ForEach(activities.prefix(3)) { activity in
                        ActivityCard(activity: activity)
                    }
                }
            }
        }
    }
}

struct ActivityCard: View {
    let activity: DashboardActivity

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: activity.icon)
                .font(.title3)
                .foregroundColor(activity.color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text(activity.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)

                Text(activity.subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(activity.timestamp, style: .relative)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if let accuracy = activity.accuracyScore {
                Text("\(Int(accuracy))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(Color("NiraPrimary"))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color("NiraPrimary").opacity(0.1))
                    )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.cardBackground)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
}

struct EmptyActivityCard: View {
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: "clock.circle")
                .font(.title)
                .foregroundColor(.gray)

            Text("No recent activity")
                .font(.subheadline)
                .foregroundColor(.secondary)

            Text("Start learning to see your progress here!")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct ConnectedAchievementShowcaseView: View {
    let achievements: [DashboardAchievement]

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("You Flex Collection 🏆")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primaryText)

                Spacer()

                if !achievements.isEmpty {
                    Button("View All") {
                        // TODO: Navigate to achievements view
                    }
                    .font(.subheadline)
                    .foregroundColor(.niraPrimary)
                }
            }

            if achievements.isEmpty {
                EmptyAchievementsCard()
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(achievements.prefix(5)) { achievement in
                            AchievementBadgeCard(achievement: achievement)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }
}

struct AchievementBadgeCard: View {
    let achievement: DashboardAchievement

    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(achievement.difficulty.color.gradient)
                    .frame(width: 60, height: 60)

                Text(achievement.icon)
                    .font(.title2)
            }

            VStack(spacing: 2) {
                Text(achievement.title)
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)

                Text(achievement.earnedAt, style: .date)
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Text("\(achievement.pointsReward) pts")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(achievement.difficulty.color)
            }
        }
        .frame(width: 100, height: 120)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.cardBackground)
                .shadow(color: achievement.difficulty.color.opacity(0.3), radius: 4, x: 0, y: 2)
        )
    }
}

struct EmptyAchievementsCard: View {
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: "star.circle")
                .font(.title)
                .foregroundColor(.gray)

            Text("Start learning to unlock achievements!")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Text("Complete lessons, maintain streaks, and master skills to earn badges")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Animated Daily Goal Component

struct AnimatedDailyGoalView: View {
    let completed: Int
    let goal: Int
    @Binding var particleAnimation: Bool

    private var progress: Double {
        guard goal > 0 else { return 0.0 }
        let calculatedProgress = Double(completed) / Double(goal)
        // Ensure we never return NaN or infinity
        guard calculatedProgress.isFinite else { return 0.0 }
        return min(calculatedProgress, 1.0)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Today's Goal 🎯")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primaryText)

                Spacer()

                Text("\(completed)/\(goal) lessons")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraAccent)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.niraAccent.opacity(0.2))
                    .cornerRadius(12)
            }

            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 12)

                RoundedRectangle(cornerRadius: 10)
                    .fill(
                        LinearGradient(
                            colors: [.niraSuccess, .niraSecondary],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: max(0, CGFloat(progress) * UIScreen.main.bounds.width * 0.8), height: 12)
                    .animation(.easeInOut(duration: 1.5), value: progress)
            }

            if completed >= goal {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.niraSuccess)
                        .font(.title2)

                    Text("Goal achieved! You're on fire! 🔥")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.niraSuccess)
                }
                .padding()
                .background(Color.niraSuccess.opacity(0.2))
                .cornerRadius(12)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(20)
        .shadow(color: .niraSuccess.opacity(0.3), radius: 8, x: 0, y: 4)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.5)) {
                // No need to animate progress, as it's handled by the binding
            }
        }
    }
}

// MARK: - Colorful Language Selection Component

struct EnhancedLanguageSelectionView: View {
    @Binding var selectedLanguage: Language

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Choose Your Adventure 🚀")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 20) {
                    ForEach(Language.allCases, id: \.self) { language in
                        ColorfulLanguageCardView(
                            language: language,
                            isSelected: selectedLanguage == language
                        ) {
                            withAnimation(.spring()) {
                                selectedLanguage = language
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

struct ColorfulLanguageCardView: View {
    let language: Language
    let isSelected: Bool
    let action: () -> Void

    private var cardGradient: [Color] {
        switch language {
        case .french: return Color.primaryGradient
        case .english: return Color.primaryGradient
        case .spanish: return Color.primaryGradient
        case .japanese: return Color.primaryGradient
        case .tamil: return Color.primaryGradient
        case .korean: return Color.primaryGradient
        case .italian: return Color.primaryGradient
        case .german: return Color.primaryGradient
        case .hindi: return Color.primaryGradient
        case .chinese: return Color.primaryGradient
        case .portuguese: return Color.primaryGradient
        case .telugu: return Color.primaryGradient
        case .vietnamese: return Color.primaryGradient
        case .indonesian: return Color.primaryGradient
        case .arabic: return Color.primaryGradient
        // New 10 languages
        case .kannada: return Color.primaryGradient
        case .malayalam: return Color.primaryGradient
        case .bengali: return Color.primaryGradient
        case .marathi: return Color.primaryGradient
        case .punjabi: return Color.primaryGradient
        case .dutch: return Color.primaryGradient
        case .swedish: return Color.primaryGradient
        case .thai: return Color.primaryGradient
        case .russian: return Color.primaryGradient
        case .norwegian: return Color.primaryGradient
        // Additional 25 languages
        case .gujarati: return Color.primaryGradient
        case .odia: return Color.primaryGradient
        case .assamese: return Color.primaryGradient
        case .konkani: return Color.primaryGradient
        case .sindhi: return Color.primaryGradient
        case .bhojpuri: return Color.primaryGradient
        case .maithili: return Color.primaryGradient
        case .swahili: return Color.primaryGradient
        case .hebrew: return Color.primaryGradient
        case .greek: return Color.primaryGradient
        case .turkish: return Color.primaryGradient
        case .farsi: return Color.primaryGradient
        case .tagalog: return Color.primaryGradient
        case .ukrainian: return Color.primaryGradient
        case .danish: return Color.primaryGradient
        case .xhosa: return Color.primaryGradient
        case .zulu: return Color.primaryGradient
        case .amharic: return Color.primaryGradient
        case .quechua: return Color.primaryGradient
        case .maori: return Color.primaryGradient
        case .cherokee: return Color.primaryGradient
        case .navajo: return Color.primaryGradient
        case .hawaiian: return Color.primaryGradient
        case .inuktitut: return Color.primaryGradient
        case .yoruba: return Color.primaryGradient
        default: return Color.primaryGradient
        }
    }

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Text(language.flag)
                    .font(.system(size: 40))

                Text(language.displayName)
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .frame(width: 100, height: 100)
            .background(
                LinearGradient(
                    colors: cardGradient,
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(20)
            .scaleEffect(isSelected ? 1.1 : 1.0)
            .shadow(color: isSelected ? cardGradient[0].opacity(0.6) : Color.clear, radius: 10, x: 0, y: 5)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(Color.white.opacity(0.5), lineWidth: isSelected ? 3 : 0)
            )
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
    }
}

// MARK: - Gradient Quick Actions Component

struct FloatingGlassQuickActionsView: View {
    let actions = [
        ("Start Lesson", "book.fill", Color.primaryGradient),
        ("Practice Speaking", "mic.fill", Color.primaryGradient),
        ("Cultural Sim", "globe", Color.primaryGradient),
        ("Review Words", "repeat", Color.primaryGradient)
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions ⚡")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(Array(actions.enumerated()), id: \.offset) { index, action in
                    FloatingGlassCard(
                        title: action.0,
                        icon: action.1,
                        gradient: action.2
                    ) {
                        // TODO: Navigate based on action
                    }
                }
            }
        }
    }
}

struct FloatingGlassCard: View {
    let title: String
    let icon: String
    let gradient: [Color]
    let action: () -> Void
    @State private var isPressed = false
    @State private var hoverAnimation = false

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(
                        LinearGradient(
                            colors: gradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .clipShape(Circle())
                    .shadow(color: gradient[0].opacity(0.5), radius: 8, x: 0, y: 4)

                Text(title)
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.primaryText)
                    .multilineTextAlignment(.center)
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.glassBorder, lineWidth: 1)
                    )
            )
            .scaleEffect(isPressed ? 0.95 : (hoverAnimation ? 1.02 : 1.0))
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .onAppear {
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                hoverAnimation = true
            }
        }
    }
}

// MARK: - Colorful Recommendations Component

struct ColorfulRecommendationsView: View {
    let language: Language

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recommended for You ✨")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(0..<3) { index in
                        ColorfulRecommendationCard(
                            title: sampleRecommendations[index].title,
                            lessonDescription: sampleRecommendations[index].lessonDescription,
                            difficulty: sampleRecommendations[index].difficulty,
                            duration: sampleRecommendations[index].duration,
                            gradient: recommendationGradients[index]
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private var recommendationGradients: [[Color]] {
        [
            Color.primaryGradient,
            Color.primaryGradient,
            Color.primaryGradient
        ]
    }

    private var sampleRecommendations: [(title: String, lessonDescription: String, difficulty: Difficulty, duration: Int)] {
        switch language {
        case .french:
            return [
                ("Café Ordering ☕", "Learn to order coffee in French", .beginner, 15),
                ("Past Tense 📚", "Master French past tense", .intermediate, 20),
                ("Business French 💼", "Professional conversations", .advanced, 25)
            ]
        case .japanese:
            return [
                ("Subway Navigation 🚇", "Navigate Tokyo subway", .beginner, 18),
                ("Keigo Basics 🙇", "Polite Japanese forms", .intermediate, 22),
                ("Business Cards 💳", "Meishi exchange etiquette", .advanced, 20)
            ]
        default:
            return [
                ("Basic Vocabulary 📖", "Essential words and phrases", .beginner, 15),
                ("Grammar Fundamentals 🔤", "Core grammar concepts", .intermediate, 20),
                ("Advanced Conversation 💬", "Complex discussions", .advanced, 25)
            ]
        }
    }
}

struct ColorfulRecommendationCard: View {
    let title: String
    let lessonDescription: String
    let difficulty: Difficulty
    let duration: Int
    let gradient: [Color]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .lineLimit(1)

            Text(lessonDescription)
                .font(.caption)
                .foregroundColor(.white.opacity(0.9))
                .lineLimit(2)

            HStack {
                Label(difficulty.displayName, systemImage: difficulty.icon)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.8))

                Spacer()

                Text("\(duration)m")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.white.opacity(0.3))
                    .cornerRadius(8)
            }
        }
        .padding(16)
        .frame(width: 200, height: 120)
        .background(
            LinearGradient(
                colors: gradient,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(20)
        .shadow(color: gradient[0].opacity(0.4), radius: 8, x: 0, y: 4)
    }
}

// MARK: - Animated Recent Activity Component

struct AnimatedRecentActivityView: View {
    @State private var showActivities = false

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Activity 📈")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            VStack(spacing: 12) {
                ForEach(Array(activityData.enumerated()), id: \.offset) { index, activity in
                    AnimatedActivityRowView(
                        title: activity.title,
                        subtitle: activity.subtitle,
                        time: activity.time,
                        color: activity.color
                    )
                    .opacity(showActivities ? 1 : 0)
                    .offset(x: showActivities ? 0 : 50)
                    .animation(.easeOut(duration: 0.5).delay(Double(index) * 0.1), value: showActivities)
                }
            }
        }
        .onAppear {
            showActivities = true
        }
    }

    private var activityData: [(title: String, subtitle: String, time: String, color: Color)] {
        [
            ("French Café Conversation ☕", "Completed • 85% score", "2h ago", .niraSuccess),
            ("Spanish Vocabulary 📚", "In Progress • 6/10 exercises", "1d ago", .niraWarning),
            ("Japanese Pronunciation 🗣️", "Completed • 92% score", "2d ago", .niraSuccess)
        ]
    }
}

struct AnimatedActivityRowView: View {
    let title: String
    let subtitle: String
    let time: String
    let color: Color

    var body: some View {
        HStack(spacing: 16) {
            Circle()
                .fill(color)
                .frame(width: 12, height: 12)
                .shadow(color: color.opacity(0.6), radius: 4, x: 0, y: 2)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }

            Spacer()

            Text(time)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondaryText)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.gray.opacity(0.2))
                .cornerRadius(8)
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - Lessons View - Completely Rewritten for Supabase Integration

struct LearningCompanionsView: View {
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var showingChat = false
    @State private var selectedCompanion: LearningCompanion?
    @State private var selectedPersona: CompanionPersona?

    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            ModernAgentsHeader(
                selectedLanguage: userPreferences.selectedLanguage.displayName,
                searchText: .constant("")
            )

            // Filter Controls
            filterControlsView

            // Main content
            mainContentView
        }
        .background(Color(.systemGroupedBackground))
        .navigationBarHidden(true)
        .sheet(isPresented: $showingChat) {
            if let companion = selectedCompanion {
                LearningCompanionChatView(companion: companion)
            }
        }
    }

    private var filterControlsView: some View {
        VStack(spacing: 16) {
            // Persona Filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    PersonaFilterChip(
                        title: "All",
                        isSelected: selectedPersona == nil
                    ) {
                        selectedPersona = nil
                    }

                    ForEach(CompanionPersona.allCases.filter { $0.isAvailableFor(userPreferences.selectedLanguage) }, id: \.self) { persona in
                        PersonaFilterChip(
                            title: persona.rawValue,
                            isSelected: selectedPersona == persona
                        ) {
                            selectedPersona = persona
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 16)
        .background(
            LinearGradient(
                colors: [Color(.systemBackground), Color(.systemGray6).opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }

    private var mainContentView: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(filteredCompanions, id: \.id) { companion in
                    CompanionCard(companion: companion) {
                        selectedCompanion = companion
                        showingChat = true
                    }
                }
            }
            .padding()
        }
    }

    private var filteredCompanions: [LearningCompanion] {
        let allCompanions = LearningCompanion.getCompanions(for: userPreferences.selectedLanguage)

        if let selectedPersona = selectedPersona {
            return allCompanions.filter { $0.persona == selectedPersona }
        }

        return allCompanions
    }
}

// MARK: - Learning Companion Personas

enum CompanionPersona: String, CaseIterable {
    case beginnerEnthusiast = "Beginner Enthusiast"
    case busyProfessional = "Busy Professional"
    case culturalSeeker = "Cultural Seeker"
    case socialLearner = "Social Learner"
    case nriHelper = "NRI Helper"
    case masterGuide = "Master Guide"

    var icon: String {
        switch self {
        case .beginnerEnthusiast: return "🌟"
        case .busyProfessional: return "💼"
        case .culturalSeeker: return "🏛️"
        case .socialLearner: return "👥"
        case .nriHelper: return "🌍"
        case .masterGuide: return "🎓"
        }
    }

    var description: String {
        switch self {
        case .beginnerEnthusiast:
            return "Patient and encouraging, perfect for starting your language journey with confidence"
        case .busyProfessional:
            return "Efficient and practical, focused on business communication and travel essentials"
        case .culturalSeeker:
            return "Deep cultural knowledge, traditions, customs, and authentic cultural immersion"
        case .socialLearner:
            return "Interactive and conversational, great for practicing real-world social situations"
        case .nriHelper:
            return "Understands diaspora experience, bridging heritage language with modern life"
        case .masterGuide:
            return "Comprehensive expertise, can handle any topic with advanced language skills"
        }
    }

    var specialties: [String] {
        switch self {
        case .beginnerEnthusiast:
            return ["Basic vocabulary", "Pronunciation", "Simple conversations", "Encouragement"]
        case .busyProfessional:
            return ["Business language", "Travel phrases", "Professional communication", "Time-efficient learning"]
        case .culturalSeeker:
            return ["Cultural traditions", "Historical context", "Customs & etiquette", "Authentic expressions"]
        case .socialLearner:
            return ["Casual conversations", "Social situations", "Group interactions", "Modern slang"]
        case .nriHelper:
            return ["Heritage connection", "Family communication", "Cultural bridge", "Diaspora experience"]
        case .masterGuide:
            return ["Advanced grammar", "Literature", "Complex topics", "All skill levels"]
        }
    }

    var isAvailableFor: (Language) -> Bool {
        switch self {
        case .nriHelper:
            // Only available for Indian languages
            return { language in
                [.tamil, .hindi, .telugu].contains(language)
            }
        default:
            // Available for all languages
            return { _ in true }
        }
    }
}

struct LearningCompanion {
    let id = UUID()
    let persona: CompanionPersona
    let language: Language
    let name: String
    let avatar: String
    let description: String
    let systemPrompt: String

    static func getCompanions(for language: Language) -> [LearningCompanion] {
        return CompanionPersona.allCases
            .filter { $0.isAvailableFor(language) }
            .map { persona in
                LearningCompanion(
                    persona: persona,
                    language: language,
                    name: generateName(for: persona, language: language),
                    avatar: persona.icon,
                    description: persona.description,
                    systemPrompt: generateSystemPrompt(for: persona, language: language)
                )
            }
    }

    private static func generateName(for persona: CompanionPersona, language: Language) -> String {
        let baseNames: [Language: [CompanionPersona: String]] = [
            .french: [
                .beginnerEnthusiast: "Sophie",
                .busyProfessional: "Laurent",
                .culturalSeeker: "Amélie",
                .socialLearner: "Pierre",
                .masterGuide: "Marie"
            ],
            .spanish: [
                .beginnerEnthusiast: "Elena",
                .busyProfessional: "Carlos",
                .culturalSeeker: "Isabella",
                .socialLearner: "Diego",
                .masterGuide: "Carmen"
            ],
            .english: [
                .beginnerEnthusiast: "Emma",
                .busyProfessional: "James",
                .culturalSeeker: "Oliver",
                .socialLearner: "Sophia",
                .masterGuide: "William"
            ],
            .japanese: [
                .beginnerEnthusiast: "Yuki",
                .busyProfessional: "Hiroshi",
                .culturalSeeker: "Sakura",
                .socialLearner: "Takeshi",
                .masterGuide: "Akiko"
            ],
            .tamil: [
                .beginnerEnthusiast: "Priya",
                .busyProfessional: "Ravi",
                .culturalSeeker: "Meera",
                .socialLearner: "Karthik",
                .nriHelper: "Deepa",
                .masterGuide: "Suresh"
            ],
            .korean: [
                .beginnerEnthusiast: "Minji",
                .busyProfessional: "Junho",
                .culturalSeeker: "Soyeon",
                .socialLearner: "Hyunwoo",
                .masterGuide: "Jisoo"
            ],
            .italian: [
                .beginnerEnthusiast: "Giulia",
                .busyProfessional: "Marco",
                .culturalSeeker: "Francesca",
                .socialLearner: "Luca",
                .masterGuide: "Valentina"
            ],
            .german: [
                .beginnerEnthusiast: "Anna",
                .busyProfessional: "Klaus",
                .culturalSeeker: "Greta",
                .socialLearner: "Max",
                .masterGuide: "Ingrid"
            ],
            .hindi: [
                .beginnerEnthusiast: "Anita",
                .busyProfessional: "Rajesh",
                .culturalSeeker: "Kavya",
                .socialLearner: "Arjun",
                .nriHelper: "Sunita",
                .masterGuide: "Vikram"
            ],
            .chinese: [
                .beginnerEnthusiast: "Li Wei",
                .busyProfessional: "Zhang Ming",
                .culturalSeeker: "Wang Mei",
                .socialLearner: "Chen Hao",
                .masterGuide: "Liu Yan"
            ],
            .portuguese: [
                .beginnerEnthusiast: "Ana",
                .busyProfessional: "João",
                .culturalSeeker: "Beatriz",
                .socialLearner: "Pedro",
                .masterGuide: "Carla"
            ],
            .telugu: [
                .beginnerEnthusiast: "Lakshmi",
                .busyProfessional: "Venkat",
                .culturalSeeker: "Sita",
                .socialLearner: "Krishna",
                .nriHelper: "Radha",
                .masterGuide: "Ramesh"
            ],
            .vietnamese: [
                .beginnerEnthusiast: "Linh",
                .busyProfessional: "Duc",
                .culturalSeeker: "Mai",
                .socialLearner: "Tuan",
                .masterGuide: "Hoa"
            ],
            .indonesian: [
                .beginnerEnthusiast: "Sari",
                .busyProfessional: "Budi",
                .culturalSeeker: "Dewi",
                .socialLearner: "Andi",
                .masterGuide: "Indira"
            ],
            .arabic: [
                .beginnerEnthusiast: "Fatima",
                .busyProfessional: "Ahmed",
                .culturalSeeker: "Aisha",
                .socialLearner: "Omar",
                .masterGuide: "Khalid"
            ]
        ]

        return baseNames[language]?[persona] ?? "Companion"
    }

    private static func generateSystemPrompt(for persona: CompanionPersona, language: Language) -> String {
        let languageName = language.displayName
        let basePrompt = """
        You are a specialized language learning companion for \(languageName).

        STRICT GUIDELINES:
        - ONLY discuss language learning topics related to \(languageName)
        - NEVER engage in conversations outside of language education
        - If asked about non-educational topics, politely redirect to language learning
        - Focus exclusively on your specialized role as described below
        - Maintain professional educational boundaries at all times

        """

        switch persona {
        case .beginnerEnthusiast:
            return basePrompt + """
            ROLE: Beginner Enthusiast Companion
            - You are patient, encouraging, and perfect for beginners
            - Focus on basic vocabulary, pronunciation, and simple conversations
            - Always provide positive reinforcement and build confidence
            - Break down complex concepts into simple, digestible parts
            - Use encouraging language and celebrate small victories
            """

        case .busyProfessional:
            return basePrompt + """
            ROLE: Busy Professional Companion
            - You are efficient and practical, focused on business communication
            - Prioritize business language, travel phrases, and professional communication
            - Provide time-efficient learning strategies
            - Focus on immediately practical language skills
            - Keep conversations concise and goal-oriented
            """

        case .culturalSeeker:
            return basePrompt + """
            ROLE: Cultural Seeker Companion
            - You have deep knowledge of \(languageName) culture, traditions, and customs
            - Provide authentic cultural context for language learning
            - Explain cultural nuances, etiquette, and historical background
            - Share traditional expressions and their cultural significance
            - Help learners understand the cultural context behind the language
            """

        case .socialLearner:
            return basePrompt + """
            ROLE: Social Learner Companion
            - You are interactive and conversational, great for social situations
            - Focus on casual conversations, social interactions, and modern usage
            - Practice real-world social scenarios and group interactions
            - Teach contemporary slang and informal expressions appropriately
            - Encourage interactive and engaging conversations
            """

        case .nriHelper:
            return basePrompt + """
            ROLE: NRI Helper Companion (for Indian languages)
            - You understand the diaspora experience and heritage language connection
            - Help bridge heritage language with modern life
            - Understand the unique challenges of maintaining cultural language abroad
            - Provide context for family communication and cultural traditions
            - Support reconnection with cultural roots through language
            """

        case .masterGuide:
            return basePrompt + """
            ROLE: Master Guide Companion
            - You have comprehensive expertise in all aspects of \(languageName)
            - Can handle advanced grammar, literature, and complex topics
            - Adapt to any skill level from beginner to advanced
            - Provide detailed explanations and comprehensive language guidance
            - Serve as the most knowledgeable companion for all language needs
            """
        }
    }
}

struct LanguageTutor {
    let id = UUID()
    let name: String
    let language: Language
    let avatar: String
    let personality: String
    let specialty: String
    let description: String
    let level: String
    let rating: Double
    let conversationCount: Int

    static let mockAgents: [LanguageTutor] = [
        // French Tutors
        LanguageTutor(
            name: "French Teacher",
            language: .french,
            avatar: "👩‍🏫",
            personality: "Friendly & Patient",
            specialty: "Conversational French",
            description: "Bonjour! I'm your French teacher from Paris. I love helping students discover the beauty of French culture through language.",
            level: "All Levels",
            rating: 4.9,
            conversationCount: 1247
        ),
        LanguageTutor(
            name: "French Chef",
            language: .french,
            avatar: "👨‍🍳",
            personality: "Enthusiastic & Cultural",
            specialty: "French Cuisine & Culture",
            description: "Salut! I'm a chef from Lyon who teaches French through cooking and cultural experiences.",
            level: "Intermediate+",
            rating: 4.8,
            conversationCount: 892
        ),

        // Spanish Tutors
        LanguageTutor(
            name: "Spanish Teacher",
            language: .spanish,
            avatar: "👩‍🎨",
            personality: "Creative & Energetic",
            specialty: "Latin American Spanish",
            description: "¡Hola! I'm your Spanish teacher from Mexico City. I teach Spanish through art, music, and cultural stories.",
            level: "All Levels",
            rating: 4.9,
            conversationCount: 1156
        ),
        LanguageTutor(
            name: "Business Spanish Teacher",
            language: .spanish,
            avatar: "👨‍💼",
            personality: "Professional & Structured",
            specialty: "Business Spanish",
            description: "Buenos días! I specialize in business Spanish and professional communication.",
            level: "Advanced",
            rating: 4.7,
            conversationCount: 743
        ),

        // Japanese Tutors
        LanguageTutor(
            name: "Japanese Teacher",
            language: .japanese,
            avatar: "👩‍🎓",
            personality: "Gentle & Traditional",
            specialty: "Japanese Culture & Etiquette",
            description: "こんにちは! I'm your Japanese teacher from Kyoto. I teach Japanese language alongside traditional culture and customs.",
            level: "All Levels",
            rating: 4.9,
            conversationCount: 967
        ),
        LanguageTutor(
            name: "Modern Japanese Teacher",
            language: .japanese,
            avatar: "👨‍💻",
            personality: "Modern & Tech-Savvy",
            specialty: "Modern Japanese & Anime",
            description: "よろしく! I'm your modern Japanese teacher from Tokyo. I teach modern Japanese through anime, games, and pop culture.",
            level: "Beginner-Intermediate",
            rating: 4.8,
            conversationCount: 1334
        ),

        // Tamil Tutors
        LanguageTutor(
            name: "Tamil Teacher",
            language: .tamil,
            avatar: "👩‍🏫",
            personality: "Warm & Encouraging",
            specialty: "Tamil Literature & Poetry",
            description: "வணக்கம்! I'm your Tamil teacher from Chennai. I teach Tamil through beautiful literature and classical poetry.",
            level: "All Levels",
            rating: 4.8,
            conversationCount: 654
        ),
        LanguageTutor(
            name: "Tamil Cinema Teacher",
            language: .tamil,
            avatar: "👨‍🎭",
            personality: "Dramatic & Expressive",
            specialty: "Tamil Cinema & Drama",
            description: "வணக்கம்! I'm your Tamil cinema teacher, a Tamil cinema enthusiast who teaches through movies and dramatic expressions.",
            level: "Intermediate+",
            rating: 4.7,
            conversationCount: 432
        ),

        // English Tutors
        LanguageTutor(
            name: "English Teacher",
            language: .english,
            avatar: "👩‍💼",
            personality: "Professional & Articulate",
            specialty: "Business English",
            description: "Hello! I'm your English teacher from London. I specialize in business English and professional communication.",
            level: "Advanced",
            rating: 4.9,
            conversationCount: 1543
        ),
        LanguageTutor(
            name: "American English Teacher",
            language: .english,
            avatar: "👨‍🎸",
            personality: "Casual & Fun",
            specialty: "American Slang & Culture",
            description: "Hey there! I'm your American English teacher from California. I teach American English through music and pop culture.",
            level: "All Levels",
            rating: 4.8,
            conversationCount: 1287
        )
    ]
}

// MARK: - Companion Card View

struct CompanionCard: View {
    let companion: LearningCompanion
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with avatar and persona
                HStack {
                    Text(companion.avatar)
                        .font(.system(size: 32))

                    VStack(alignment: .leading, spacing: 2) {
                        Text(companion.name)
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(1)

                        Text(companion.persona.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                            .lineLimit(1)
                    }

                    Spacer()
                }

                // Description
                Text(companion.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)

                Spacer()

                // Chat button
                HStack {
                    Spacer()
                    Text("Start Chat")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
            }
            .padding()
            .frame(height: 140)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct PersonaFilterChip: View {
    let title: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            Text(title)
                .font(.subheadline)
                .fontWeight(isSelected ? .semibold : .regular)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? Color.blue : Color(.systemGray6))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AIAgentCard: View {
    let agent: LanguageTutor
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                // Avatar and Rating
                HStack {
                    Text(agent.avatar)
                        .font(.system(size: 40))

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        HStack(spacing: 2) {
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                            Text(String(format: "%.1f", agent.rating))
                                .font(.caption2)
                                .fontWeight(.medium)
                        }

                        Text("\(agent.conversationCount) chats")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // Name and Level
                VStack(alignment: .leading, spacing: 4) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(agent.level)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.niraPrimary.opacity(0.2))
                        .foregroundColor(.niraPrimary)
                        .cornerRadius(8)
                }

                // Specialty
                Text(agent.specialty)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraSecondary)

                // Personality
                Text(agent.personality)
                    .font(.caption)
                    .foregroundColor(.secondary)

                // Description
                Text(agent.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)

                // Chat Button
                HStack {
                    Image(systemName: "message.fill")
                        .font(.caption)
                    Text("Start Chat")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color("NiraPrimary").gradient)
                .cornerRadius(12)
            }
            .padding()
            .frame(height: 280)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .niraPrimary.opacity(isPressed ? 0.2 : 0.1), radius: isPressed ? 8 : 4, x: 0, y: isPressed ? 4 : 2)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.niraPrimary)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

// MARK: - Learning Companion Chat View

struct LearningCompanionChatView: View {
    let companion: LearningCompanion
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            VStack {
                // Header
                VStack(spacing: 12) {
                    Text(companion.avatar)
                        .font(.system(size: 60))

                    Text(companion.name)
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(companion.persona.rawValue)
                        .font(.subheadline)
                        .foregroundColor(.niraPrimary)

                    Text("Specialized for \(companion.language.displayName)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()

                Spacer()

                // Coming Soon Message
                VStack(spacing: 16) {
                    Image(systemName: "message.badge.waveform")
                        .font(.system(size: 50))
                        .foregroundColor(.niraPrimary)

                    Text("Advanced Chat Coming Soon")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("We're building an advanced multi-agent system with LangGraph/CrewAI for personalized learning conversations.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }

                Spacer()
            }
            .navigationTitle("Chat with \(companion.name)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ContentView()
        .modelContainer(for: Item.self, inMemory: true)
}
