//
//  HomeView.swift
//  NIRA
//
//  Created by NIRA Team on 1/2025.
//

import SwiftUI

// MARK: - Premium Home View with Approved 21st Dev Design

struct HomeView: View {
    @Binding var selectedTab: Int
    @State private var currentStreak = 7
    @State private var goals = [
        Goal(id: "1", title: "Complete daily lesson", isCompleted: true),
        Goal(id: "2", title: "Practice speaking for 10 minutes", isCompleted: true),
        Goal(id: "3", title: "Review 20 vocabulary words", isCompleted: false)
    ]
    @State private var showingAddGoal = false
    @State private var newGoalText = ""
    
    // Learning metrics for today
    @State private var todayMetrics = [
        LearningMetric(label: "Lessons", value: "3", progress: 100, color: .emojiBlue, unit: "complete"),
        LearningMetric(label: "Speaking", value: "15", progress: 75, color: .emojiPurple, unit: "min"),
        LearningMetric(label: "Vocabulary", value: "42", progress: 84, color: .emojiOrange, unit: "words")
    ]
    
    private let selectedLanguage = UserPreferencesService.shared.selectedLanguage
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // Welcome Header
                    welcomeHeader
                    
                    // Daily Goals Card
                    dailyGoalsCard
                    
                    // Today's Progress Metrics
                    todayProgressSection
                    
                    // Quick Actions
                    quickActionsSection
                    
                    // Language Portfolio
                    languagePortfolioCard
                    
                    // Month 1 Features
                    month1FeaturesCard
                    
                    // Script Literacy Features (conditional)
                    if selectedLanguage.requiresScriptLiteracy {
                        scriptLiteracyCard
                    }
                    
                    // Recommended Section
                    recommendedSection
                }
                .padding()
                .padding(.bottom, 100) // Extra padding for tab bar
            }
            .background(
                LinearGradient(
                    colors: [Color(.systemBackground), Color(.systemBackground).opacity(0.8)],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle("")
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingAddGoal) {
            addGoalSheet
        }
    }
    
    // MARK: - Welcome Header
    private var welcomeHeader: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Welcome back! 👋")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(greetingMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Streak Counter
                HStack(spacing: 8) {
                    Image(systemName: "flame.fill")
                        .foregroundColor(.emojiOrange)
                        .font(.title3)
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("\(currentStreak)")
                            .font(.title3)
                            .fontWeight(.bold)
                        Text("day streak")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.emojiOrange.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.emojiOrange.opacity(0.3), lineWidth: 1)
                )
            }
            
            // Language Selection
            HStack(spacing: 12) {
                Image(systemName: "globe")
                    .foregroundColor(.emojiBlue)
                    .font(.title2)
                    .frame(width: 40, height: 40)
                    .background(Color.emojiBlue.opacity(0.1))
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Learning")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(selectedLanguage.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                                    RegionalLanguageSelector()
            }
            .padding()
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
    }
    
    // MARK: - Daily Goals Card
    private var dailyGoalsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: "target")
                        .foregroundColor(.emojiGreen)
                        .font(.title3)
                    
                    Text("Today's Goals")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                Text("\(completedGoalsCount)/\(goals.count) complete")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray5))
                    .clipShape(Capsule())
            }
            
            // Progress Bar
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("Progress")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(Int(goalProgress))%")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                ProgressView(value: goalProgress, total: 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: .emojiGreen))
                    .scaleEffect(x: 1, y: 1.5, anchor: .center)
            }
            
            // Goals List
            VStack(spacing: 8) {
                ForEach(goals) { goal in
                    goalRow(goal)
                }
            }
            
            // Add Goal / Completion Status
            HStack {
                if completedGoalsCount == goals.count {
                    HStack(spacing: 8) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.emojiGreen)
                        Text("All goals completed! Great job!")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.emojiGreen)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.emojiGreen.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                } else {
                    Button(action: { showingAddGoal = true }) {
                        HStack(spacing: 6) {
                            Image(systemName: "plus")
                                .font(.caption)
                            Text("Add Goal")
                                .font(.caption)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 6)
                        .background(Color(.systemGray6))
                        .clipShape(Capsule())
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Today's Progress Section
    private var todayProgressSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Today's Progress")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 16) {
                ForEach(todayMetrics) { metric in
                    metricCard(metric)
                }
            }
        }
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                quickActionCard(
                    title: "Start Lesson",
                    icon: "book.fill",
                    color: .emojiBlue,
                    action: { selectedTab = 1 }
                )
                
                quickActionCard(
                    title: "Practice Writing",
                    icon: "pencil",
                    color: .emojiGreen,
                    action: { selectedTab = 2 }
                )
                
                quickActionCard(
                    title: "Cultural Insights",
                    icon: "globe",
                    color: .emojiPurple,
                    action: { selectedTab = 1 }
                )
                
                quickActionCard(
                    title: "Review Words",
                    icon: "text.book.closed",
                    color: .emojiOrange,
                    action: { selectedTab = 2 }
                )
            }
        }
    }
    
    // MARK: - Language Portfolio Card
    private var languagePortfolioCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 8) {
                Image(systemName: "globe")
                    .foregroundColor(.emojiBlue)
                    .font(.title3)
                
                Text("Language Portfolio")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            Text("Your learning journey across multiple languages")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack(spacing: 24) {
                portfolioStat(value: "73", label: "Total\nLanguages", icon: "globe", color: .emojiBlue)
                portfolioStat(value: "4", label: "Started", icon: "book.fill", color: .emojiGreen)
                portfolioStat(value: "0", label: "Completed", icon: "checkmark.circle.fill", color: .emojiOrange)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Month 1 Features Card
    private var month1FeaturesCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("NEW: Month 1 Features")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("Just Released")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.emojiOrange.opacity(0.2))
                    .foregroundColor(.emojiOrange)
                    .clipShape(Capsule())
            }
            
            Text("Explore our newest learning tools")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                featureCard(title: "FSRS Reviews", subtitle: "Spaced repetition", icon: "brain.head.profile", color: .emojiPurple)
                featureCard(title: "Assessments", subtitle: "Skill evaluation", icon: "checkmark.circle", color: .emojiGreen)
                featureCard(title: "Analytics", subtitle: "Learning insights", icon: "chart.bar", color: .emojiBlue)
                featureCard(title: "Dashboard", subtitle: "Learning hub", icon: "target", color: .emojiOrange)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.emojiOrange.opacity(0.3), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Script Literacy Card
    private var scriptLiteracyCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("\(selectedLanguage.displayName) Script Literacy")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("Priority 1")
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.emojiPink)
                    .foregroundColor(.white)
                    .clipShape(Capsule())
            }
            
            Text("Master the writing system to unlock advanced features")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack(spacing: 16) {
                scriptFeatureCard(title: "Learn Script", subtitle: "Introduction", icon: "book.fill", color: .emojiBlue)
                scriptFeatureCard(title: "Read", subtitle: "Script practice", icon: "text.book.closed", color: .emojiGreen)
                scriptFeatureCard(title: "Write", subtitle: "Hand practice", icon: "pencil", color: .emojiOrange)
            }
            
            HStack {
                Text("Complete script literacy to unlock advanced features")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("0% Complete")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray5))
                    .clipShape(Capsule())
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.emojiBlue.opacity(0.3), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Recommended Section
    private var recommendedSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 8) {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.emojiPurple)
                    .font(.title3)
                
                Text("Recommended for You")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            Text("Personalized suggestions to enhance your learning")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack(spacing: 12) {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.emojiBlue)
                    .font(.title2)
                    .frame(width: 40, height: 40)
                    .background(Color.emojiBlue.opacity(0.1))
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("AI is learning about you")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text("Complete more lessons to get personalized recommendations")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button(action: {}) {
                    Image(systemName: "arrow.up.right")
                        .font(.caption)
                        .foregroundColor(.emojiBlue)
                        .frame(width: 24, height: 24)
                        .background(Color.emojiBlue.opacity(0.1))
                        .clipShape(Circle())
                }
            }
            .padding()
            .background(Color(.systemGray6).opacity(0.5))
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Helper Views
    
    private func goalRow(_ goal: Goal) -> some View {
        Button(action: { toggleGoal(goal.id) }) {
            HStack(spacing: 12) {
                Image(systemName: goal.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(goal.isCompleted ? .emojiGreen : .secondary)
                    .font(.title3)
                
                Text(goal.title)
                    .font(.subheadline)
                    .foregroundColor(goal.isCompleted ? .secondary : .primary)
                    .strikethrough(goal.isCompleted)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color(.systemGray6).opacity(0.5))
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func metricCard(_ metric: LearningMetric) -> some View {
        VStack(spacing: 12) {
            ZStack {
                Circle()
                    .stroke(Color(.systemGray5), lineWidth: 4)
                    .frame(width: 60, height: 60)
                
                Circle()
                    .trim(from: 0, to: metric.progress / 100)
                    .stroke(metric.color, lineWidth: 4)
                    .frame(width: 60, height: 60)
                    .rotationEffect(.degrees(-90))
                
                VStack(spacing: 0) {
                    Text(metric.value)
                        .font(.headline)
                        .fontWeight(.bold)
                    Text(metric.unit)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            VStack(spacing: 2) {
                Text(metric.label)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("\(Int(metric.progress))% complete")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private func quickActionCard(title: String, icon: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(color.opacity(0.1))
                    .clipShape(Circle())
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                
                Text("for \(selectedLanguage.displayName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func portfolioStat(value: String, label: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(color.opacity(0.1))
                .clipShape(Circle())
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
    }
    
    private func featureCard(title: String, subtitle: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(color.opacity(0.1))
                .clipShape(Circle())
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color(.systemGray6).opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    private func scriptFeatureCard(title: String, subtitle: String, icon: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 30, height: 30)
                .background(color.opacity(0.1))
                .clipShape(Circle())
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 10)
        .background(Color(.systemGray6).opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    // MARK: - Add Goal Sheet
    private var addGoalSheet: some View {
        NavigationView {
            VStack(spacing: 20) {
                TextField("Enter new goal", text: $newGoalText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                Spacer()
            }
            .padding()
            .navigationTitle("Add Goal")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    showingAddGoal = false
                    newGoalText = ""
                },
                trailing: Button("Add") {
                    addGoal()
                }
                .disabled(newGoalText.isEmpty)
            )
        }
    }
    
    // MARK: - Helper Properties and Methods
    
    private var greetingMessage: String {
        let hour = Calendar.current.component(.hour, from: Date())
        if hour < 12 {
            return "Good morning! Ready for your daily lessons?"
        } else if hour < 18 {
            return "Good afternoon! Time to continue your learning journey!"
        } else {
            return "Good evening! Let's review what you've learned today!"
        }
    }
    
    private var completedGoalsCount: Int {
        goals.filter { $0.isCompleted }.count
    }
    
    private var goalProgress: Double {
        guard !goals.isEmpty else { return 0 }
        return Double(completedGoalsCount) / Double(goals.count) * 100
    }
    
    private func toggleGoal(_ id: String) {
        if let index = goals.firstIndex(where: { $0.id == id }) {
            goals[index].isCompleted.toggle()
        }
    }
    
    private func addGoal() {
        let newGoal = Goal(
            id: UUID().uuidString,
            title: newGoalText,
            isCompleted: false
        )
        goals.append(newGoal)
        newGoalText = ""
        showingAddGoal = false
    }
}

// MARK: - Supporting Data Models

struct Goal: Identifiable {
    let id: String
    var title: String
    var isCompleted: Bool
}

struct LearningMetric: Identifiable {
    let id = UUID()
    let label: String
    let value: String
    let progress: Double
    let color: Color
    let unit: String
}

struct HomeView_Previews: PreviewProvider {
    static var previews: some View {
        HomeView(selectedTab: .constant(0))
    }
}