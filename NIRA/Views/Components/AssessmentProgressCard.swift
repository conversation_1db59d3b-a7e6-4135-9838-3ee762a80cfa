import SwiftUI

struct AssessmentProgressCard: View {
    let skillCategory: SkillCategory
    let currentProgress: Double
    let targetProgress: Double
    let recentScores: [Double]
    let showDetailedView: Bool
    
    init(
        skillCategory: SkillCategory,
        currentProgress: Double,
        targetProgress: Double = 1.0,
        recentScores: [Double] = [],
        showDetailedView: Bool = false
    ) {
        self.skillCategory = skillCategory
        self.currentProgress = currentProgress
        self.targetProgress = targetProgress
        self.recentScores = recentScores
        self.showDetailedView = showDetailedView
    }
    
    var body: some View {
        VStack(spacing: 12) {
            if showDetailedView {
                detailedView
            } else {
                compactView
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.secondarySystemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(skillCategory.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Compact View
    private var compactView: some View {
        VStack(spacing: 8) {
            // Header with icon and skill name
            HStack {
                Image(systemName: skillCategory.icon)
                    .foregroundColor(skillCategory.color)
                    .font(.title3)
                    .frame(width: 24)
                
                Text(skillCategory.displayName)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // Progress percentage
                Text("\(Int(currentProgress * 100))%")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(progressColor)
            }
            
            // Progress bar
            progressBar
            
            // Recent trend indicator
            if !recentScores.isEmpty {
                trendIndicator
            }
        }
    }
    
    // MARK: - Detailed View
    private var detailedView: some View {
        VStack(spacing: 16) {
            // Header section
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: skillCategory.icon)
                            .foregroundColor(skillCategory.color)
                            .font(.title2)
                        
                        Text(skillCategory.displayName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                    }
                    
                    Text(skillCategory.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Large progress indicator
                VStack {
                    Text("\(Int(currentProgress * 100))%")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(progressColor)
                    
                    Text("Mastery")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Detailed progress bar
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Progress to Mastery")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(Int(currentProgress * 100))/\(Int(targetProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                progressBar
            }
            
            // Performance insights
            if !recentScores.isEmpty {
                performanceInsights
            }
        }
    }
    
    // MARK: - Progress Bar
    private var progressBar: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Background track
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color(.systemGray5))
                    .frame(height: 8)
                
                // Progress fill
                RoundedRectangle(cornerRadius: 6)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                skillCategory.color.opacity(0.7),
                                skillCategory.color
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(
                        width: geometry.size.width * currentProgress,
                        height: 8
                    )
                    .animation(.easeInOut(duration: 0.5), value: currentProgress)
                
                // Target indicator (if different from 100%)
                if targetProgress < 1.0 {
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.primary.opacity(0.6))
                        .frame(width: 2, height: 12)
                        .offset(x: geometry.size.width * targetProgress - 1)
                }
            }
        }
        .frame(height: 8)
    }
    
    // MARK: - Trend Indicator
    private var trendIndicator: some View {
        HStack(spacing: 4) {
            Image(systemName: trendDirection.icon)
                .foregroundColor(trendDirection.color)
                .font(.caption)
            
            Text(trendDirection.description)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
    
    // MARK: - Performance Insights
    private var performanceInsights: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Performance")
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack {
                // Average score
                VStack(alignment: .leading, spacing: 2) {
                    Text("Average")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(Int(averageScore * 100))%")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(scoreColor(averageScore))
                }
                
                Spacer()
                
                // Improvement indicator
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Trend")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 4) {
                        Image(systemName: trendDirection.icon)
                            .foregroundColor(trendDirection.color)
                        
                        Text(trendDirection.description)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(trendDirection.color)
                    }
                }
            }
            
            // Mini chart of recent scores
            if recentScores.count > 1 {
                miniPerformanceChart
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.tertiarySystemBackground))
        )
    }
    
    // MARK: - Mini Performance Chart
    private var miniPerformanceChart: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Last \(recentScores.count) Assessments")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(alignment: .bottom, spacing: 2) {
                ForEach(Array(recentScores.enumerated()), id: \.offset) { index, score in
                    Rectangle()
                        .fill(scoreColor(score))
                        .frame(
                            width: 20,
                            height: max(4, score * 30)
                        )
                        .cornerRadius(2)
                        .animation(.easeInOut(duration: 0.3).delay(Double(index) * 0.1), value: score)
                }
            }
            .frame(height: 30)
        }
    }
    
    // MARK: - Computed Properties
    private var progressColor: Color {
        switch currentProgress {
        case 0.9...: return .green
        case 0.7..<0.9: return .blue
        case 0.5..<0.7: return .orange
        default: return .red
        }
    }
    
    private var averageScore: Double {
        guard !recentScores.isEmpty else { return 0.0 }
        return recentScores.reduce(0, +) / Double(recentScores.count)
    }
    
    private var trendDirection: ProgressTrendDirection {
        guard recentScores.count >= 2 else { return .stable }
        
        let recent = recentScores.suffix(3)
        let older = recentScores.prefix(recentScores.count - 1).suffix(3)
        
        let recentAvg = recent.reduce(0, +) / Double(recent.count)
        let olderAvg = older.reduce(0, +) / Double(older.count)
        
        let difference = recentAvg - olderAvg
        
        if difference > 0.05 {
            return .improving
        } else if difference < -0.05 {
            return .declining
        } else {
            return .stable
        }
    }
    
    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 0.8...: return .green
        case 0.6..<0.8: return .yellow
        default: return .red
        }
    }
}

// MARK: - Supporting Types
enum ProgressTrendDirection {
    case improving
    case stable
    case declining
    
    var icon: String {
        switch self {
        case .improving: return "arrow.up.right"
        case .stable: return "arrow.right"
        case .declining: return "arrow.down.right"
        }
    }
    
    var color: Color {
        switch self {
        case .improving: return .green
        case .stable: return .blue
        case .declining: return .red
        }
    }
    
    var description: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Needs Focus"
        }
    }
}

extension SkillCategory {
    var description: String {
        switch self {
        case .vocabulary: return "Word knowledge and usage"
        case .grammar: return "Sentence structure and rules"
        case .listening: return "Audio comprehension skills"
        case .speaking: return "Pronunciation and fluency"
        case .reading: return "Text comprehension abilities"
        case .writing: return "Written communication skills"
        case .culture: return "Cultural context understanding"
        case .pronunciation: return "Phonetic accuracy"
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        // Compact view examples
        AssessmentProgressCard(
            skillCategory: .vocabulary,
            currentProgress: 0.75,
            recentScores: [0.8, 0.7, 0.85, 0.9]
        )
        
        AssessmentProgressCard(
            skillCategory: .grammar,
            currentProgress: 0.45,
            recentScores: [0.4, 0.5, 0.4, 0.5]
        )
        
        // Detailed view example
        AssessmentProgressCard(
            skillCategory: .culture,
            currentProgress: 0.85,
            recentScores: [0.7, 0.8, 0.85, 0.9, 0.8],
            showDetailedView: true
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
} 