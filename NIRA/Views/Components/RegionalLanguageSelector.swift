import SwiftUI

struct RegionalLanguageSelector: View {
    @ObservedObject var userPreferences = UserPreferencesService.shared
    @State private var showingLanguageSelector = false
    
    var body: some View {
        Button(action: {
            showingLanguageSelector = true
        }) {
            HStack(spacing: 10) {
                // Flag with subtle shadow
                Text(getSelectedLanguageFlag())
                    .font(.title2)
                    .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(userPreferences.selectedLanguage.displayName)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text("Change")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6).opacity(0.8))
                    .shadow(color: .black.opacity(0.06), radius: 6, x: 0, y: 3)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray5), lineWidth: 0.5)
                    )
            )
        }
        .sheet(isPresented: $showingLanguageSelector) {
            ColorfulRegionalLanguageSelectorSheet()
                .environmentObject(userPreferences)
        }
    }
    
    private func getSelectedLanguageFlag() -> String {
        switch userPreferences.selectedLanguage {
        case .tamil, .hindi, .bengali, .urdu, .punjabi, .gujarati, .telugu, .kannada, .malayalam, .marathi:
            return "🇮🇳"
        case .english:
            return "🇺🇸"
        case .spanish:
            return "🇪🇸"
        case .french:
            return "🇫🇷"
        case .german:
            return "🇩🇪"
        case .italian:
            return "🇮🇹"
        case .portuguese:
            return "🇵🇹"
        case .chinese:
            return "🇨🇳"
        case .japanese:
            return "🇯🇵"
        case .korean:
            return "🇰🇷"
        case .russian:
            return "🇷🇺"
        case .arabic:
            return "🇸🇦"
        case .hebrew:
            return "🇮🇱"
        case .turkish:
            return "🇹🇷"
        case .farsi:
            return "🇮🇷"
        case .thai:
            return "🇹🇭"
        case .vietnamese:
            return "🇻🇳"
        case .indonesian:
            return "🇮🇩"
        case .tagalog:
            return "🇵🇭"
        default:
            return "🌍"
        }
    }
}

struct ColorfulRegionalLanguageSelectorSheet: View {
    @EnvironmentObject var userPreferences: UserPreferencesService
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedRegion: LanguageRegion?
    @State private var searchText = ""
    
    var body: some View {
        NavigationView {
            ZStack {
                // Dynamic background based on selected region
                backgroundGradient
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    headerView
                    
                    if selectedRegion == nil {
                        // Regional selection view
                        regionsGridView
                    } else {
                        // Language selection for specific region
                        languageSelectionView
                    }
                }
            }
            .navigationBarHidden(true)
        }
    }
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: selectedRegion?.backgroundColors ?? [
                Color(.systemBackground),
                Color(.systemGray6).opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                if selectedRegion != nil {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedRegion = nil
                        }
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 14, weight: .medium))
                            Text("Regions")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.blue)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.1))
                        .clipShape(Capsule())
                    }
                    .transition(.scale.combined(with: .opacity))
                }
                
                Spacer()
                
                Button("Done") {
                    presentationMode.wrappedValue.dismiss()
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            VStack(alignment: .leading, spacing: 6) {
                Text(selectedRegion?.title ?? "Choose Your Region")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(selectedRegion == nil ? "Select a region to explore languages" : "Pick your language")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 20)
            .padding(.bottom, 16)
        }
    }
    
    private var regionsGridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 16),
                GridItem(.flexible(), spacing: 16)
            ], spacing: 20) {
                ForEach(LanguageRegion.allCases, id: \.self) { region in
                    ColorfulRegionCard(
                        region: region,
                        onSelect: {
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                selectedRegion = region
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 40)
        }
    }
    
    private var languageSelectionView: some View {
        ScrollView {
            VStack(spacing: 16) {
                if let selectedRegion = selectedRegion {
                    ForEach(selectedRegion.languages, id: \.0.rawValue) { language, flag in
                        ColorfulLanguageRow(
                            language: language,
                            flag: flag,
                            isSelected: userPreferences.selectedLanguage == language,
                            regionColors: selectedRegion.primaryColors,
                            onSelect: {
                                userPreferences.selectedLanguage = language
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    presentationMode.wrappedValue.dismiss()
                                }
                            }
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 40)
        }
    }
}

struct ColorfulRegionCard: View {
    let region: LanguageRegion
    let onSelect: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 16) {
                // Region Icon/Emoji
                Text(region.emoji)
                    .font(.system(size: 40))
                    .shadow(color: (region.primaryColors.first?.opacity(0.3)) ?? Color.blue.opacity(0.3), radius: 4, x: 0, y: 2)
                
                VStack(spacing: 8) {
                    Text(region.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("\(region.languages.count) languages")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    // Language flags preview
                    HStack(spacing: 4) {
                        ForEach(region.languages.prefix(4), id: \.0.rawValue) { _, flag in
                            Text(flag)
                                .font(.caption)
                        }
                        if region.languages.count > 4 {
                            Text("+\(region.languages.count - 4)")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 180)
            .padding(.vertical, 20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: region.gradientColors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(
                        color: (region.primaryColors.first?.opacity(isPressed ? 0.4 : 0.2)) ?? Color.blue.opacity(0.2),
                        radius: isPressed ? 8 : 12,
                        x: 0,
                        y: isPressed ? 4 : 8
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.3),
                                        Color.clear
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

struct ColorfulLanguageRow: View {
    let language: Language
    let flag: String
    let isSelected: Bool
    let regionColors: [Color]
    let onSelect: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                // Flag with region-themed background
                Text(flag)
                    .font(.title2)
                    .frame(width: 50, height: 50)
                    .background(
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: isSelected ? regionColors : [Color(.systemGray6), Color(.systemGray5)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .shadow(color: isSelected ? (regionColors.first?.opacity(0.3) ?? Color.blue.opacity(0.3)) : Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(language.displayName)
                        .font(.body)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    if language.requiresScriptLiteracy {
                        Text("Script-based language")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if isSelected {
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: regionColors,
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 28, height: 28)
                                                         .shadow(color: (regionColors.first?.opacity(0.3)) ?? Color.blue.opacity(0.3), radius: 4, x: 0, y: 2)
                        
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        isSelected ?
                        LinearGradient(
                            colors: regionColors.map { $0.opacity(0.1) },
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            colors: [Color(.systemBackground), Color(.systemGray6).opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(
                        color: Color.black.opacity(isPressed ? 0.15 : (isSelected ? 0.1 : 0.05)),
                        radius: isPressed ? 4 : 8,
                        x: 0,
                        y: isPressed ? 2 : 4
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ?
                                LinearGradient(
                                    colors: regionColors.map { $0.opacity(0.3) },
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ) :
                                LinearGradient(
                                    colors: [Color.white.opacity(0.2), Color.clear],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: isSelected ? 1.5 : 1
                            )
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

enum LanguageRegion: CaseIterable {
    case southAsia
    case eastAsia
    case europe
    case middleEast
    case southeastAsia
    case americas
    
    var title: String {
        switch self {
        case .southAsia: return "South Asia"
        case .eastAsia: return "East Asia"
        case .europe: return "Europe"
        case .middleEast: return "Middle East"
        case .southeastAsia: return "Southeast Asia"
        case .americas: return "Americas"
        }
    }
    
    var emoji: String {
        switch self {
        case .southAsia: return "🏔️"
        case .eastAsia: return "🏮"
        case .europe: return "🏰"
        case .middleEast: return "🕌"
        case .southeastAsia: return "🌺"
        case .americas: return "🗽"
        }
    }
    
    var primaryColors: [Color] {
        switch self {
        case .southAsia:
            return [Color.orange, Color.red]
        case .eastAsia:
            return [Color.red, Color.yellow]
        case .europe:
            return [Color.blue, Color.purple]
        case .middleEast:
            return [Color.green, Color.teal]
        case .southeastAsia:
            return [Color.pink, Color.purple]
        case .americas:
            return [Color.blue, Color.green]
        }
    }
    
    var gradientColors: [Color] {
        switch self {
        case .southAsia:
            return [Color.orange.opacity(0.3), Color.red.opacity(0.2)]
        case .eastAsia:
            return [Color.red.opacity(0.3), Color.yellow.opacity(0.2)]
        case .europe:
            return [Color.blue.opacity(0.3), Color.purple.opacity(0.2)]
        case .middleEast:
            return [Color.green.opacity(0.3), Color.teal.opacity(0.2)]
        case .southeastAsia:
            return [Color.pink.opacity(0.3), Color.purple.opacity(0.2)]
        case .americas:
            return [Color.blue.opacity(0.3), Color.green.opacity(0.2)]
        }
    }
    
    var backgroundColors: [Color] {
        switch self {
        case .southAsia:
            return [Color(.systemBackground), Color.orange.opacity(0.05)]
        case .eastAsia:
            return [Color(.systemBackground), Color.red.opacity(0.05)]
        case .europe:
            return [Color(.systemBackground), Color.blue.opacity(0.05)]
        case .middleEast:
            return [Color(.systemBackground), Color.green.opacity(0.05)]
        case .southeastAsia:
            return [Color(.systemBackground), Color.pink.opacity(0.05)]
        case .americas:
            return [Color(.systemBackground), Color.blue.opacity(0.05)]
        }
    }
    
    var languages: [(Language, String)] {
        switch self {
        case .southAsia:
            return [
                (.tamil, "🇮🇳"),
                (.hindi, "🇮🇳"),
                (.bengali, "🇧🇩"),
                (.urdu, "🇵🇰"),
                (.punjabi, "🇮🇳"),
                (.gujarati, "🇮🇳"),
                (.telugu, "🇮🇳"),
                (.kannada, "🇮🇳"),
                (.malayalam, "🇮🇳"),
                (.marathi, "🇮🇳")
            ]
        case .eastAsia:
            return [
                (.chinese, "🇨🇳"),
                (.japanese, "🇯🇵"),
                (.korean, "🇰🇷")
            ]
        case .europe:
            return [
                (.spanish, "🇪🇸"),
                (.french, "🇫🇷"),
                (.german, "🇩🇪"),
                (.italian, "🇮🇹"),
                (.portuguese, "🇵🇹"),
                (.russian, "🇷🇺"),
                (.dutch, "🇳🇱"),
                (.polish, "🇵🇱")
            ]
        case .middleEast:
            return [
                (.arabic, "🇸🇦"),
                (.hebrew, "🇮🇱"),
                (.turkish, "🇹🇷"),
                (.farsi, "🇮🇷")
            ]
        case .southeastAsia:
            return [
                (.thai, "🇹🇭"),
                (.vietnamese, "🇻🇳"),
                (.indonesian, "🇮🇩"),
                (.tagalog, "🇵🇭")
            ]
        case .americas:
            return [
                (.english, "🇺🇸"),
                (.spanish, "🇲🇽"),
                (.portuguese, "🇧🇷")
            ]
        }
    }
}

#Preview {
    RegionalLanguageSelector()
}
 