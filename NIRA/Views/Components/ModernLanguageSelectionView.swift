//
//  ModernLanguageSelectionView.swift
//  NIRA
//
//  Created by NIRA Team on 29/05/2025.
//

import SwiftUI

// MARK: - Language Categories for Better Organization

enum LanguageCategory: String, CaseIterable {
    case popular = "Popular"
    case indian = "Indian Languages"
    case european = "European"
    case asian = "Asian & Pacific"
    case african = "African"
    case indigenous = "Indigenous & Heritage"
    case middleEastern = "Middle Eastern"

    var icon: String {
        switch self {
        case .popular: return "star.fill"
        case .indian: return "🇮🇳"
        case .european: return "🇪🇺"
        case .asian: return "🌏"
        case .african: return "🌍"
        case .indigenous: return "🏛️"
        case .middleEastern: return "🕌"
        }
    }

    var languages: [Language] {
        switch self {
        case .popular:
            return [.english, .spanish, .french, .german, .italian, .portuguese, .chinese, .japanese, .korean, .arabic]
        case .indian:
            return [.hindi, .tamil, .telugu, .kannada, .malayalam, .bengali, .marathi, .punjabi, .gujarati, .odia, .assamese, .konkani, .sindhi, .bhojpuri, .maithili]
        case .european:
            return [.french, .german, .italian, .spanish, .portuguese, .dutch, .swedish, .norwegian, .danish, .greek, .ukrainian, .russian]
        case .asian:
            return [.chinese, .japanese, .korean, .vietnamese, .indonesian, .thai, .tagalog]
        case .african:
            return [.swahili, .xhosa, .zulu, .yoruba, .amharic]
        case .indigenous:
            return [.quechua, .maori, .cherokee, .navajo, .hawaiian, .inuktitut]
        case .middleEastern:
            return [.arabic, .hebrew, .farsi, .turkish]
        }
    }
}

// MARK: - Language Tier System

enum LanguageTier: String, CaseIterable {
    case tier1 = "Full Features"
    case tier2 = "Partial Features"
    case tier3 = "Basic Features"

    var description: String {
        switch self {
        case .tier1: return "Voice, Live Chat, Full AI"
        case .tier2: return "Text Chat, Limited AI"
        case .tier3: return "Text Only, Basic AI"
        }
    }

    var color: Color {
        switch self {
        case .tier1: return .green
        case .tier2: return .orange
        case .tier3: return .blue
        }
    }

    var icon: String {
        switch self {
        case .tier1: return "crown.fill"
        case .tier2: return "star.fill"
        case .tier3: return "circle.fill"
        }
    }

    static func getTier(for language: Language) -> LanguageTier {
        let tier1Languages: [Language] = [
            .english, .spanish, .french, .german, .italian, .portuguese, .chinese, .japanese, .korean, .arabic,
            .hindi, .tamil, .telugu, .kannada, .malayalam, .bengali, .marathi, .punjabi, .gujarati, .russian, .dutch
        ]

        let tier2Languages: [Language] = [
            .vietnamese, .indonesian, .thai, .swedish, .norwegian, .danish, .greek, .ukrainian, .hebrew, .farsi,
            .turkish, .tagalog, .odia, .assamese, .konkani, .sindhi, .bhojpuri, .maithili
        ]

        if tier1Languages.contains(language) {
            return .tier1
        } else if tier2Languages.contains(language) {
            return .tier2
        } else {
            return .tier3
        }
    }
}

// MARK: - Modern Language Selection View

struct ModernLanguageSelectionView: View {
    @Binding var selectedLanguage: Language
    let onLanguageSelected: (Language) -> Void

    @State private var searchText = ""
    @State private var selectedCategory: LanguageCategory? = .popular
    @State private var selectedTier: LanguageTier? = nil
    @State private var showingSearch = false
    @State private var viewMode: ViewMode = .grid
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared

    enum ViewMode: String, CaseIterable {
        case grid = "Grid"
        case list = "List"
        case category = "Category"

        var icon: String {
            switch self {
            case .grid: return "square.grid.2x2"
            case .list: return "list.bullet"
            case .category: return "folder"
            }
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            headerSection
            filterSection

            if showingSearch {
                searchSection
            }

            languageContentSection
        }
        .background(Color(.systemBackground))
    }

    // MARK: - Header Section

    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Choose Your Language")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("50 languages • AI-powered learning")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                HStack(spacing: 12) {
                    Button(action: { showingSearch.toggle() }) {
                        Image(systemName: showingSearch ? "xmark.circle.fill" : "magnifyingglass")
                            .font(.title3)
                            .foregroundColor(.primary)
                    }

                    Menu {
                        ForEach(ViewMode.allCases, id: \.self) { mode in
                            Button(action: { viewMode = mode }) {
                                Label(mode.rawValue, systemImage: mode.icon)
                            }
                        }
                    } label: {
                        Image(systemName: viewMode.icon)
                            .font(.title3)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    // MARK: - Filter Section

    private var filterSection: some View {
        VStack(spacing: 12) {
            // Category Filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    Button("All") {
                        selectedCategory = nil
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(selectedCategory == nil ? Color.blue : Color(.systemGray6))
                    .foregroundColor(selectedCategory == nil ? .white : .primary)
                    .cornerRadius(12)

                    ForEach(LanguageCategory.allCases, id: \.self) { category in
                        Button(category.rawValue) {
                            selectedCategory = category
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(selectedCategory == category ? Color.blue : Color(.systemGray6))
                        .foregroundColor(selectedCategory == category ? .white : .primary)
                        .cornerRadius(12)
                    }
                }
                .padding(.horizontal)
            }

            // Tier Filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    Button("All Tiers") {
                        selectedTier = nil
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(selectedTier == nil ? Color.blue : Color(.systemGray6))
                    .foregroundColor(selectedTier == nil ? .white : .primary)
                    .cornerRadius(12)

                    ForEach(LanguageTier.allCases, id: \.self) { tier in
                        TierFilterChip(
                            tier: tier,
                            isSelected: selectedTier == tier,
                            action: { selectedTier = tier }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
    }

    // MARK: - Search Section

    private var searchSection: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search languages...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
        .padding(.bottom, 8)
    }

    // MARK: - Language Content Section

    private var languageContentSection: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if viewMode == .category {
                    categoryView
                } else {
                    languageGridOrList
                }
            }
            .padding()
        }
    }

    private var filteredLanguages: [Language] {
        var languages = Language.allCases

        // Apply category filter
        if let selectedCategory = selectedCategory {
            languages = selectedCategory.languages
        }

        // Apply tier filter
        if let selectedTier = selectedTier {
            languages = languages.filter { LanguageTier.getTier(for: $0) == selectedTier }
        }

        // Apply search filter
        if !searchText.isEmpty {
            languages = languages.filter {
                $0.displayName.localizedCaseInsensitiveContains(searchText)
            }
        }

        return languages
    }

    private var languageGridOrList: some View {
        Group {
            if viewMode == .grid {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                    ForEach(filteredLanguages, id: \.self) { language in
                        ModernLanguageCard(
                            language: language,
                            isSelected: selectedLanguage == language,
                            progress: dashboardCoordinator.getLanguageProgress(for: language),
                            onSelect: {
                                withAnimation(.spring()) {
                                    selectedLanguage = language
                                    onLanguageSelected(language)
                                }
                            }
                        )
                    }
                }
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(filteredLanguages, id: \.self) { language in
                        ModernLanguageListItem(
                            language: language,
                            isSelected: selectedLanguage == language,
                            progress: dashboardCoordinator.getLanguageProgress(for: language),
                            onSelect: {
                                withAnimation(.spring()) {
                                    selectedLanguage = language
                                    onLanguageSelected(language)
                                }
                            }
                        )
                    }
                }
            }
        }
    }

    private var categoryView: some View {
        LazyVStack(spacing: 20) {
            ForEach(LanguageCategory.allCases, id: \.self) { category in
                CategorySection(
                    category: category,
                    selectedLanguage: $selectedLanguage,
                    onLanguageSelected: onLanguageSelected
                )
            }
        }
    }
}

// MARK: - Supporting Components

struct TierFilterChip: View {
    let tier: LanguageTier
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: tier.icon)
                    .font(.caption)
                Text(tier.rawValue)
                    .font(.subheadline)
                    .fontWeight(isSelected ? .semibold : .regular)
            }
            .foregroundColor(isSelected ? .white : tier.color)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? tier.color : tier.color.opacity(0.1))
            )
            .overlay(
                Capsule()
                    .stroke(tier.color.opacity(0.3), lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct ModernLanguageCard: View {
    let language: Language
    let isSelected: Bool
    let progress: LanguageProgress
    let onSelect: () -> Void

    private var tier: LanguageTier {
        LanguageTier.getTier(for: language)
    }

    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 12) {
                // Header with flag and tier
                HStack {
                    Text(language.flag)
                        .font(.title)

                    Spacer()

                    HStack(spacing: 4) {
                        Image(systemName: tier.icon)
                            .font(.caption2)
                        Text(tier.rawValue)
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(tier.color)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(tier.color.opacity(0.1))
                    .cornerRadius(8)
                }

                // Language name
                VStack(spacing: 4) {
                    Text(language.displayName)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(isSelected ? .white : .primary)
                        .lineLimit(1)

                    Text(progress.progressText)
                        .font(.caption)
                        .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
                }

                // Progress bar
                ProgressView(value: progress.progressPercentage)
                    .progressViewStyle(LinearProgressViewStyle(tint: isSelected ? .white : .niraPrimary))
                    .scaleEffect(y: 0.8)

                // Stats
                HStack {
                    Text("\(progress.completedLessons)")
                        .font(.caption)
                        .fontWeight(.semibold)
                    Text("lessons")
                        .font(.caption)

                    Spacer()

                    Text("\(Int(progress.progressPercentage * 100))%")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                .foregroundColor(isSelected ? .white.opacity(0.9) : .secondary)
            }
            .padding()
            .frame(height: 140)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.niraPrimary : Color(.systemBackground))
                    .shadow(
                        color: isSelected ? Color.niraPrimary.opacity(0.3) : .black.opacity(0.1),
                        radius: isSelected ? 8 : 4,
                        x: 0,
                        y: isSelected ? 4 : 2
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? Color.clear : Color(.systemGray4), lineWidth: 1)
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ModernLanguageListItem: View {
    let language: Language
    let isSelected: Bool
    let progress: LanguageProgress
    let onSelect: () -> Void

    private var tier: LanguageTier {
        LanguageTier.getTier(for: language)
    }

    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                // Flag and tier indicator
                VStack(spacing: 4) {
                    Text(language.flag)
                        .font(.title2)

                    Image(systemName: tier.icon)
                        .font(.caption)
                        .foregroundColor(tier.color)
                }
                .frame(width: 50)

                // Language info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(language.displayName)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Spacer()

                        Text(tier.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(tier.color)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(tier.color.opacity(0.1))
                            .cornerRadius(8)
                    }

                    Text(progress.progressText)
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    // Progress bar
                    ProgressView(value: progress.progressPercentage)
                        .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
                        .scaleEffect(y: 0.8)

                    HStack {
                        Text("\(progress.completedLessons) lessons completed")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text("\(Int(progress.progressPercentage * 100))%")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.niraPrimary)
                    }
                }

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.niraPrimary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.niraPrimary.opacity(0.1) : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.niraPrimary : Color(.systemGray4), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct CategorySection: View {
    let category: LanguageCategory
    @Binding var selectedLanguage: Language
    let onLanguageSelected: (Language) -> Void
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Category header
            HStack {
                HStack(spacing: 8) {
                    Text(category.icon)
                        .font(.title3)

                    Text(category.rawValue)
                        .font(.headline)
                        .fontWeight(.bold)
                }

                Spacer()

                Text("\(category.languages.count) languages")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }

            // Languages grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                ForEach(category.languages, id: \.self) { language in
                    ModernLanguageCard(
                        language: language,
                        isSelected: selectedLanguage == language,
                        progress: dashboardCoordinator.getLanguageProgress(for: language),
                        onSelect: {
                            withAnimation(.spring()) {
                                selectedLanguage = language
                                onLanguageSelected(language)
                            }
                        }
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6).opacity(0.3))
        )
    }
}
