//
//  AgentViewComponents.swift
//  NIRA
//
//  Missing UI Components for Enhanced Agents View
//  Based on Language Learning Content Development Standards v2.0
//

import SwiftUI

// MARK: - Enhanced Agent Detail View
struct EnhancedAgentDetailView: View {
    let agent: LearningAgent
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Agent Avatar and Info
                VStack(spacing: 12) {
                    Text(agent.avatar)
                        .font(.system(size: 80))
                    
                    Text(agent.name)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text(agent.description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                // Specialties
                VStack(alignment: .leading, spacing: 8) {
                    Text("Specialties")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                        ForEach(agent.specialties, id: \.self) { specialty in
                            Text(specialty)
                                .font(.caption)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.niraThemeIndigo.opacity(0.1))
                                .foregroundColor(.niraThemeIndigo)
                                .cornerRadius(12)
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 12) {
                    Button("Start Learning Session") {
                        // Action to start learning
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    
                    Button("View Learning Path") {
                        // Action to view path
                    }
                    .buttonStyle(SecondaryButtonStyle())
                }
                .padding(.horizontal)
            }
            .navigationTitle("AI Companion")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - Personalization Panel View
struct PersonalizationPanelView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var learningStyle = "Visual"
    @State private var sessionLength = 15.0
    @State private var difficultyLevel = "Adaptive"
    
    var body: some View {
        NavigationView {
            Form {
                Section("Learning Preferences") {
                    Picker("Learning Style", selection: $learningStyle) {
                        Text("Visual").tag("Visual")
                        Text("Auditory").tag("Auditory")
                        Text("Kinesthetic").tag("Kinesthetic")
                        Text("Mixed").tag("Mixed")
                    }
                    
                    VStack(alignment: .leading) {
                        Text("Session Length: \(Int(sessionLength)) minutes")
                        Slider(value: $sessionLength, in: 5...30, step: 5)
                    }
                    
                    Picker("Difficulty", selection: $difficultyLevel) {
                        Text("Beginner").tag("Beginner")
                        Text("Intermediate").tag("Intermediate")
                        Text("Advanced").tag("Advanced")
                        Text("Adaptive").tag("Adaptive")
                    }
                }
                
                Section("Biometric Integration") {
                    Toggle("Heart Rate Monitoring", isOn: .constant(true))
                    Toggle("Stress Detection", isOn: .constant(true))
                    Toggle("Cognitive Load Assessment", isOn: .constant(false))
                }
                
                Section("Cultural Preferences") {
                    Toggle("Include Cultural Context", isOn: .constant(true))
                    Toggle("Regional Variations", isOn: .constant(false))
                    Toggle("Traditional Expressions", isOn: .constant(true))
                }
            }
            .navigationTitle("Personalization")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Filter Chips
struct SmartFilterChip: View {
    let title: String
    let count: Int
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Text("\(count)")
                    .font(.caption2)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color(.systemBackground))
                    .cornerRadius(8)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? Color.niraThemeIndigo : Color(.systemGray6))
            .foregroundColor(isSelected ? .white : .primary)
            .cornerRadius(12)
        }
    }
}

// Legacy filter chip - removed to avoid compilation conflicts
// struct EnhancedFilterChip: View { ... }

// MARK: - Button Styles
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.niraThemeIndigo)
            .foregroundColor(.white)
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color(.systemGray6))
            .foregroundColor(.primary)
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}

// Color extensions are defined in ColorExtensions.swift 