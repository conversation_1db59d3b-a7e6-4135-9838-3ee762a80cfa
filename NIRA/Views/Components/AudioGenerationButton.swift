import SwiftUI

struct AudioGenerationButton: View {
    let text: String
    let onAudioGenerated: (URL) -> Void
    
    @StateObject private var elevenLabsService = ElevenLabsService.shared
    @State private var isGenerating = false
    
    var body: some View {
        Button(action: {
            Task {
                await generateAudio()
            }
        }) {
            HStack(spacing: 8) {
                if isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Image(systemName: "waveform.badge.plus")
                        .font(.system(size: 16, weight: .medium))
                }
                
                Text(isGenerating ? "Generating..." : "Generate Audio")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isGenerating ? Color.gray : Color.blue)
            )
        }
        .disabled(isGenerating || text.isEmpty)
    }
    
    private func generateAudio() async {
        isGenerating = true
        
        do {
            let audioURL = try await elevenLabsService.generateAudio(text: text)
            onAudioGenerated(audioURL)
        } catch {
            print("❌ Audio generation failed: \(error)")
        }
        
        isGenerating = false
    }
}

#Preview {
    AudioGenerationButton(text: "வணக்கம்") { url in
        print("Generated audio: \(url)")
    }
}
