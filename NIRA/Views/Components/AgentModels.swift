//
//  AgentModels.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import Foundation
import SwiftUI

// MARK: - Language Tier System (extending existing LanguageTier from ModernLanguageSelectionView)
extension LanguageTier {
    var displayName: String {
        switch self {
        case .tier1: return "Tier 1"
        case .tier2: return "Tier 2"
        case .tier3: return "Tier 3"
        }
    }

    var features: [String] {
        switch self {
        case .tier1: return ["🎤 Voice Chat", "💬 Live AI Chat", "📚 Full Lessons", "🌍 Cultural Context", "🎯 All Agents"]
        case .tier2: return ["💬 AI Chat", "📚 Full Lessons", "🌍 Cultural Context", "📝 Text Practice"]
        case .tier3: return ["📚 Basic Lessons", "📝 Text Practice", "🤖 Limited AI"]
        }
    }

    var gradient: LinearGradient {
        switch self {
        case .tier1: return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .tier2: return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .tier3: return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
}

// MARK: - Agent Personas (5 Specific Personas from Agentic Framework)
enum AgentPersona: String, CaseIterable, Codable {
    case beginnerEnthusiast = "Beginner Enthusiast"
    case busyProfessional = "Busy Professional"
    case traveler = "Traveler"
    case culturalSeeker = "Cultural Seeker"
    case socialLearner = "Social Learner"

    var icon: String {
        switch self {
        case .beginnerEnthusiast: return "🌟"
        case .busyProfessional: return "💼"
        case .traveler: return "✈️"
        case .culturalSeeker: return "🏛️"
        case .socialLearner: return "👥"
        }
    }

    var description: String {
        switch self {
        case .beginnerEnthusiast:
            return "Perfect for beginners! Fun, gamified learning with pop culture references and encouraging guidance."
        case .busyProfessional:
            return "Focused on business vocabulary and professional scenarios. Perfect for career advancement."
        case .traveler:
            return "Essential travel phrases and cultural tips. Your companion for exploring new places."
        case .culturalSeeker:
            return "Dive deep into literature, traditions, and cultural heritage. Explore the soul of the language."
        case .socialLearner:
            return "Master social interactions and local phrases. Connect with native speakers confidently."
        }
    }

    var color: Color {
        switch self {
        case .beginnerEnthusiast: return .pink
        case .busyProfessional: return .indigo
        case .traveler: return .teal
        case .culturalSeeker: return .purple
        case .socialLearner: return .orange
        }
    }
}

// MARK: - Language with Tier Information (using existing LanguageTier.getTier method)
extension Language {
    var tier: LanguageTier {
        return LanguageTier.getTier(for: self)
    }

    var learnerCount: String {
        switch self {
        case .spanish: return "45.6M"
        case .french: return "26M"
        case .english: return "20M"
        case .japanese: return "21.5M"
        case .korean: return "17.7M"
        case .german: return "17.4M"
        case .italian: return "12.2M"
        case .hindi: return "11.2M"
        case .chinese: return "8.3M"
        case .russian: return "6.7M"
        case .dutch: return "3.8M"
        case .swedish: return "3.2M"
        case .norwegian: return "2.8M"
        case .turkish: return "2.2M"
        case .greek: return "2.0M"
        case .ukrainian: return "1.4M"
        case .vietnamese: return "1.2M"
        case .thai: return "1.0M"
        case .indonesian: return "0.9M"
        case .swahili: return "0.8M"
        case .tagalog: return "0.8M"
        case .hebrew: return "0.7M"
        case .bengali: return "0.6M"
        case .tamil: return "0.5M"
        case .portuguese: return "5.5M"
        case .arabic: return "4.5M"
        case .telugu: return "0.4M"
        case .yoruba: return "0.4M"
        case .marathi: return "0.3M"
        case .punjabi: return "0.3M"
        case .gujarati: return "0.3M"
        case .danish: return "0.5M"
        case .kannada: return "0.2M"
        case .malayalam: return "0.2M"
        case .odia: return "0.2M"
        case .bhojpuri: return "0.2M"
        case .assamese: return "0.1M"
        case .sindhi: return "0.1M"
        case .maithili: return "0.1M"
        case .zulu: return "0.1M"
        case .amharic: return "0.1M"
        case .konkani: return "0.05M"
        case .xhosa: return "0.05M"
        case .quechua: return "0.05M"
        case .hawaiian: return "0.03M"
        case .navajo: return "0.02M"
        case .maori: return "0.01M"
        case .cherokee: return "0.01M"
        case .inuktitut: return "0.01M"
        default: return "0.1M"
        }
    }
}

// MARK: - Learning Agent Model
struct LearningAgent: Identifiable, Hashable {
    let id = UUID()
    let persona: AgentPersona
    let language: Language
    let name: String
    let avatar: String
    let description: String
    let specialties: [String]
    let systemPrompt: String

    var tier: LanguageTier {
        language.tier
    }

    var availableFeatures: [String] {
        tier.features
    }

    static func getAllAgents() -> [LearningAgent] {
        var agents: [LearningAgent] = []

        for language in Language.allCases {
            for persona in AgentPersona.allCases {
                let agent = LearningAgent(
                    persona: persona,
                    language: language,
                    name: generateCulturalName(for: persona, language: language),
                    avatar: generateAvatar(for: persona, language: language),
                    description: persona.description,
                    specialties: generateSpecialties(for: persona, language: language),
                    systemPrompt: generateSystemPrompt(for: persona, language: language)
                )
                agents.append(agent)
            }
        }

        return agents
    }

    static func getAgents(for language: Language) -> [LearningAgent] {
        return getAllAgents().filter { $0.language == language }
    }

    static func getAgents(for persona: AgentPersona) -> [LearningAgent] {
        return getAllAgents().filter { $0.persona == persona }
    }

    static func getAgents(for tier: LanguageTier) -> [LearningAgent] {
        return getAllAgents().filter { $0.tier == tier }
    }
}

// MARK: - Agent Generation Functions
extension LearningAgent {
    private static func generateCulturalName(for persona: AgentPersona, language: Language) -> String {
        // Cultural names based on the agentic framework specifications
        let names: [Language: [AgentPersona: String]] = [
            // Tier 1 Languages
            .spanish: [
                .beginnerEnthusiast: "Elena",
                .busyProfessional: "Carlos",
                .traveler: "Marco",
                .culturalSeeker: "Isabella",
                .socialLearner: "Diego"
            ],
            .french: [
                .beginnerEnthusiast: "Sophie",
                .busyProfessional: "Laurent",
                .traveler: "Amélie",
                .culturalSeeker: "Pierre",
                .socialLearner: "Marie"
            ],
            .japanese: [
                .beginnerEnthusiast: "Aki",
                .busyProfessional: "Hiroshi",
                .traveler: "Yuki",
                .culturalSeeker: "Sakura",
                .socialLearner: "Takeshi"
            ],
            .korean: [
                .beginnerEnthusiast: "Soo-jin",
                .busyProfessional: "Min-jun",
                .traveler: "Ji-hye",
                .culturalSeeker: "Seung-ho",
                .socialLearner: "Hae-won"
            ],
            .hindi: [
                .beginnerEnthusiast: "Priya",
                .busyProfessional: "Raj",
                .traveler: "Anita",
                .culturalSeeker: "Arjun",
                .socialLearner: "Kavya"
            ],
            .tamil: [
                .beginnerEnthusiast: "Meera",
                .busyProfessional: "Ravi",
                .traveler: "Priya",
                .culturalSeeker: "Rani",
                .socialLearner: "Karthik"
            ],
            .arabic: [
                .beginnerEnthusiast: "Fatima",
                .busyProfessional: "Ahmed",
                .traveler: "Aisha",
                .culturalSeeker: "Omar",
                .socialLearner: "Khalid"
            ],
            .chinese: [
                .beginnerEnthusiast: "Li Wei",
                .busyProfessional: "Zhang Ming",
                .traveler: "Wang Mei",
                .culturalSeeker: "Chen Yu",
                .socialLearner: "Liu Xin"
            ],
            .bengali: [
                .beginnerEnthusiast: "Ravi",
                .busyProfessional: "Anil",
                .traveler: "Kiran",
                .culturalSeeker: "Rani",
                .socialLearner: "Sam"
            ],
            .russian: [
                .beginnerEnthusiast: "Katya",
                .busyProfessional: "Dmitri",
                .traveler: "Anya",
                .culturalSeeker: "Boris",
                .socialLearner: "Olga"
            ],
            .dutch: [
                .beginnerEnthusiast: "Emma",
                .busyProfessional: "Pieter",
                .traveler: "Sophie",
                .culturalSeeker: "Willem",
                .socialLearner: "Anna"
            ],
            .thai: [
                .beginnerEnthusiast: "Nim",
                .busyProfessional: "Somchai",
                .traveler: "Ploy",
                .culturalSeeker: "Kamon",
                .socialLearner: "Malee"
            ],
            .vietnamese: [
                .beginnerEnthusiast: "Linh",
                .busyProfessional: "Duc",
                .traveler: "Mai",
                .culturalSeeker: "Tuan",
                .socialLearner: "Hoa"
            ],
            .indonesian: [
                .beginnerEnthusiast: "Sari",
                .busyProfessional: "Budi",
                .traveler: "Dewi",
                .culturalSeeker: "Andi",
                .socialLearner: "Indira"
            ],
            .telugu: [
                .beginnerEnthusiast: "Ravi",
                .busyProfessional: "Anil",
                .traveler: "Kiran",
                .culturalSeeker: "Meera",
                .socialLearner: "Arjun"
            ],
            .kannada: [
                .beginnerEnthusiast: "Ravi",
                .busyProfessional: "Anil",
                .traveler: "Kiran",
                .culturalSeeker: "Meera",
                .socialLearner: "Arjun"
            ],
            .malayalam: [
                .beginnerEnthusiast: "Meera",
                .busyProfessional: "Anil",
                .traveler: "Kiran",
                .culturalSeeker: "Rani",
                .socialLearner: "Arjun"
            ],
            .marathi: [
                .beginnerEnthusiast: "Priya",
                .busyProfessional: "Anil",
                .traveler: "Kiran",
                .culturalSeeker: "Rani",
                .socialLearner: "Arjun"
            ],
            .punjabi: [
                .beginnerEnthusiast: "Simran",
                .busyProfessional: "Harpreet",
                .traveler: "Jasbir",
                .culturalSeeker: "Gurpreet",
                .socialLearner: "Manpreet"
            ]
        ]

        return names[language]?[persona] ?? "\(persona.rawValue) Guide"
    }

    private static func generateAvatar(for persona: AgentPersona, language: Language) -> String {
        // Return persona icon for now, could be enhanced with cultural avatars
        return persona.icon
    }

    private static func generateSpecialties(for persona: AgentPersona, language: Language) -> [String] {
        switch persona {
        case .beginnerEnthusiast:
            return ["Basic Vocabulary", "Pronunciation", "Fun Learning", "Confidence Building"]
        case .busyProfessional:
            return ["Business Language", "Professional Emails", "Meeting Phrases", "Career Focus"]
        case .traveler:
            return ["Travel Phrases", "Cultural Tips", "Navigation", "Local Customs"]
        case .culturalSeeker:
            return ["Literature", "History", "Traditions", "Cultural Heritage"]
        case .socialLearner:
            return ["Conversations", "Social Media", "Local Slang", "Community Integration"]
        }
    }

    private static func generateSystemPrompt(for persona: AgentPersona, language: Language) -> String {
        let languageName = language.displayName
        let tierInfo = language.tier

        let basePrompt = """
        You are a specialized \(persona.rawValue) language learning companion for \(languageName) (\(tierInfo.displayName)).

        STRICT GUIDELINES:
        - ONLY discuss language learning topics related to \(languageName)
        - NEVER engage in conversations outside of language education
        - Focus exclusively on your specialized role as a \(persona.rawValue)
        - Maintain professional educational boundaries at all times
        """

        let personaSpecific: String
        switch persona {
        case .beginnerEnthusiast:
            personaSpecific = """

            ROLE: Beginner Enthusiast Companion
            - You are patient, encouraging, and perfect for beginners
            - Focus on basic vocabulary, pronunciation, and simple conversations
            - Use gamification and pop culture references when appropriate
            - Always provide positive reinforcement and build confidence
            - Break down complex concepts into simple, digestible parts
            """
        case .busyProfessional:
            personaSpecific = """

            ROLE: Busy Professional Companion
            - Focus on business vocabulary and professional scenarios
            - Help with emails, meetings, and career-related language
            - Provide efficient, time-conscious learning approaches
            - Emphasize practical, immediately applicable skills
            """
        case .traveler:
            personaSpecific = """

            ROLE: Traveler Companion
            - Focus on essential travel phrases and cultural navigation
            - Provide practical tips for real-world situations
            - Help with ordering food, asking directions, and local customs
            - Share cultural insights and etiquette
            """
        case .culturalSeeker:
            personaSpecific = """

            ROLE: Cultural Immersion Seeker Companion
            - Dive deep into literature, traditions, and cultural heritage
            - Help explore the cultural context behind the language
            - Discuss history, art, and cultural phenomena
            - Encourage appreciation of cultural nuances
            """
        case .socialLearner:
            personaSpecific = """

            ROLE: Social Learner Companion
            - Focus on social interactions and community integration
            - Help with conversational skills and local phrases
            - Assist with understanding social media and modern communication
            - Build confidence for real-world social situations
            """
        }

        return basePrompt + personaSpecific
    }
}
