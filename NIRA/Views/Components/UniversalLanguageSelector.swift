import SwiftUI

struct UniversalLanguageSelector: View {
    @ObservedObject var userPreferences = UserPreferencesService.shared
    @State private var showingLanguageSelector = false
    
    let allLanguages: [(Language, String)] = [
        (.spanish, "🇪🇸"), (.french, "🇫🇷"), (.japanese, "🇯🇵"), (.german, "🇩🇪"),
        (.tamil, "🇮🇳"), (.chinese, "🇨🇳"), (.korean, "🇰🇷"), (.italian, "🇮🇹"),
        (.portuguese, "🇵🇹"), (.russian, "🇷🇺"), (.arabic, "🇸🇦"), (.hindi, "🇮🇳"),
        (.dutch, "🇳🇱"), (.swedish, "🇸🇪"), (.norwegian, "🇳🇴"), (.danish, "🇩🇰"),
        (.polish, "🇵🇱"), (.czech, "🇨🇿"), (.hungarian, "🇭🇺"),
        (.greek, "🇬🇷"), (.hebrew, "🇮🇱"), (.turkish, "🇹🇷"), (.vietnamese, "🇻🇳"),
        (.thai, "🇹🇭"), (.indonesian, "🇮🇩"), (.swahili, "🇰🇪"),
        (.bengali, "🇧🇩"), (.punjabi, "🇮🇳"), (.gujarati, "🇮🇳"), (.marathi, "🇮🇳"),
        (.telugu, "🇮🇳"), (.kannada, "🇮🇳"), (.malayalam, "🇮🇳"), (.urdu, "🇵🇰")
    ]
    
    var body: some View {
        Button(action: {
            showingLanguageSelector = true
        }) {
            HStack(spacing: 8) {
                Text(flagForLanguage(userPreferences.selectedLanguage))
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(userPreferences.selectedLanguage.displayName)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.primary)
                    Text("Change")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingLanguageSelector) {
            UniversalLanguageSelectorSheet(
                languages: allLanguages,
                selectedLanguage: userPreferences.selectedLanguage,
                onSelectLanguage: { language in
                    userPreferences.selectedLanguage = language
                    showingLanguageSelector = false
                }
            )
        }
    }
    
    private func flagForLanguage(_ language: Language) -> String {
        return allLanguages.first(where: { $0.0 == language })?.1 ?? "🌍"
    }
}

struct UniversalLanguageSelectorSheet: View {
    let languages: [(Language, String)]
    let selectedLanguage: Language
    let onSelectLanguage: (Language) -> Void
    @Environment(\.presentationMode) var presentationMode
    @State private var searchText = ""
    
    var filteredLanguages: [(Language, String)] {
        if searchText.isEmpty {
            return languages.sorted { $0.0.displayName < $1.0.displayName }
        } else {
            return languages.filter { 
                $0.0.displayName.localizedCaseInsensitiveContains(searchText)
            }.sorted { $0.0.displayName < $1.0.displayName }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search languages...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                    
                    if !searchText.isEmpty {
                        Button(action: { searchText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .padding(.horizontal)
                .padding(.bottom, 16)
                
                // Languages List
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(filteredLanguages, id: \.0) { language, flag in
                            LanguageRow(
                                language: language,
                                flag: flag,
                                isSelected: selectedLanguage == language,
                                onSelect: {
                                    onSelectLanguage(language)
                                }
                            )
                        }
                    }
                }
            }
            .navigationTitle("Select Language")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("Done") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
}

struct LanguageRow: View {
    let language: Language
    let flag: String
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                Text(flag)
                    .font(.title2)
                    .frame(width: 32)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(language.displayName)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    if language.requiresScriptLiteracy {
                        Text("Script-based language")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title3)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                Rectangle()
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    UniversalLanguageSelector()
} 