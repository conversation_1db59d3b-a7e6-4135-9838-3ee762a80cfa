import SwiftUI

enum AppearanceMode: String, CaseIterable {
    case system = "System"
    case light = "Light"
    case dark = "Dark"
    
    var icon: String {
        switch self {
        case .system: return "gear"
        case .light: return "sun.max.fill"
        case .dark: return "moon.fill"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .system: return nil
        case .light: return .light
        case .dark: return .dark
        }
    }
    
    var gradientColors: [Color] {
        switch self {
        case .system: return [Color.blue.opacity(0.3), Color.purple.opacity(0.3)]
        case .light: return [Color.orange.opacity(0.3), Color.yellow.opacity(0.3)]
        case .dark: return [Color.indigo.opacity(0.3), Color.black.opacity(0.3)]
        }
    }
}

class ThemeManager: ObservableObject {
    @Published var currentMode: AppearanceMode = .system
    
    init() {
        loadSavedPreference()
    }
    
    private func loadSavedPreference() {
        if let savedMode = UserDefaults.standard.string(forKey: "AppearanceMode"),
           let mode = AppearanceMode(rawValue: savedMode) {
            currentMode = mode
        }
    }
    
    func setMode(_ mode: AppearanceMode) {
        currentMode = mode
        UserDefaults.standard.set(mode.rawValue, forKey: "AppearanceMode")
        applyThemeToAllWindows(mode)
    }
    
    private func applyThemeToAllWindows(_ mode: AppearanceMode) {
        DispatchQueue.main.async {
            guard let windowScene = UIApplication.shared.connectedScenes
                .compactMap({ $0 as? UIWindowScene })
                .first else { return }
            
            for window in windowScene.windows {
                window.overrideUserInterfaceStyle = mode.colorScheme?.uiInterfaceStyle ?? .unspecified
            }
        }
    }
}

extension ColorScheme {
    var uiInterfaceStyle: UIUserInterfaceStyle {
        switch self {
        case .light: return .light
        case .dark: return .dark
        @unknown default: return .unspecified
        }
    }
}

struct ThemeToggle: View {
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showingThemeSelector = false
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            showingThemeSelector = true
        }) {
            HStack(spacing: 16) {
                // Modern icon with gradient background
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: themeManager.currentMode.gradientColors),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 44, height: 44)
                        .shadow(color: themeManager.currentMode.gradientColors.first?.opacity(0.3) ?? Color.clear, radius: 4, x: 0, y: 2)
                    
                    Image(systemName: themeManager.currentMode.icon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                }
                
                VStack(alignment: .leading, spacing: 3) {
                    Text("Appearance")
                        .font(.body)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(themeManager.currentMode.rawValue)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                    .scaleEffect(0.9)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(
                        color: Color.black.opacity(isPressed ? 0.12 : 0.06),
                        radius: isPressed ? 4 : 8,
                        x: 0,
                        y: isPressed ? 1 : 3
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.2),
                                        Color.clear
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .sheet(isPresented: $showingThemeSelector) {
            ModernThemeSelectorSheet()
                .environmentObject(themeManager)
        }
    }
}

struct ModernThemeSelectorSheet: View {
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedMode: AppearanceMode?
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(.systemBackground),
                        Color(.systemGray6).opacity(0.3)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Modern header
                    headerView
                    
                    // Theme options
                    ScrollView {
                        VStack(spacing: 16) {
                            ForEach(AppearanceMode.allCases, id: \.self) { mode in
                                ModernThemeOptionCard(
                                    mode: mode,
                                    isSelected: themeManager.currentMode == mode,
                                    onSelect: {
                                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                            selectedMode = mode
                                            themeManager.setMode(mode)
                                        }
                                        
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                            presentationMode.wrappedValue.dismiss()
                                        }
                                    }
                                )
                                .transition(.scale.combined(with: .opacity))
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 40)
                    }
                }
            }
            .navigationBarHidden(true)
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                Spacer()
                
                Button("Done") {
                    presentationMode.wrappedValue.dismiss()
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            VStack(alignment: .leading, spacing: 6) {
                Text("Appearance")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("Choose how NIRA looks")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 20)
            .padding(.bottom, 24)
        }
    }
}

struct ModernThemeOptionCard: View {
    let mode: AppearanceMode
    let isSelected: Bool
    let onSelect: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 20) {
                // Theme icon with gradient background
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: mode.gradientColors),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 56, height: 56)
                        .shadow(color: mode.gradientColors.first?.opacity(0.4) ?? Color.clear, radius: 6, x: 0, y: 3)
                    
                    Image(systemName: mode.icon)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.primary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(mode.rawValue)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(descriptionForMode(mode))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                if isSelected {
                    ZStack {
                        Circle()
                            .fill(Color.blue)
                            .frame(width: 28, height: 28)
                            .shadow(color: Color.blue.opacity(0.3), radius: 4, x: 0, y: 2)
                        
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(.systemBackground),
                                Color(.systemGray6).opacity(isSelected ? 0.5 : 0.2)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(
                        color: Color.black.opacity(isPressed ? 0.15 : (isSelected ? 0.12 : 0.08)),
                        radius: isPressed ? 6 : (isSelected ? 12 : 10),
                        x: 0,
                        y: isPressed ? 2 : (isSelected ? 4 : 3)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                isSelected ? 
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.blue.opacity(0.3),
                                        Color.blue.opacity(0.1)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ) :
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.2),
                                        Color.clear
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: isSelected ? 1.5 : 1
                            )
                    )
            )
            .scaleEffect(isPressed ? 0.97 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
    
    private func descriptionForMode(_ mode: AppearanceMode) -> String {
        switch mode {
        case .system:
            return "Automatically matches your device settings"
        case .light:
            return "Always use light appearance for better visibility"
        case .dark:
            return "Always use dark appearance to reduce eye strain"
        }
    }
}

#Preview {
    ThemeToggle()
        .environmentObject(ThemeManager())
} 