import SwiftUI

struct ExerciseDetailView: View {
    let exercises: [LessonExerciseItem]
    @Environment(\.dismiss) private var dismiss
    @State private var currentIndex = 0
    @State private var selectedAnswers: [Int?] = []
    @State private var showResults: [Bool] = []
    @State private var progress: CGFloat = 0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Animated gradient background
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.8, green: 0.7, blue: 1.0),
                        Color(red: 0.6, green: 0.4, blue: 0.9),
                        Color(red: 0.4, green: 0.2, blue: 0.8)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Floating particles effect
                ParticleView()
                    .opacity(0.3)
                
                VStack(spacing: 0) {
                    // Modern header with glass morphism
                    modernHeaderView
                    
                    // Main content with sexy cards
                    if !exercises.isEmpty {
                        TabView(selection: $currentIndex) {
                            ForEach(Array(exercises.enumerated()), id: \.offset) { index, exercise in
                                ModernExerciseCard(
                                    exercise: exercise,
                                    exerciseIndex: index,
                                    selectedAnswer: binding(for: index),
                                    showResult: showResultBinding(for: index)
                                )
                                .tag(index)
                            }
                        }
                        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                        .background(Color.clear)
                    } else {
                        modernEmptyStateView
                    }
                    
                    // Modern bottom controls
                    modernBottomControls
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            selectedAnswers = Array(repeating: nil, count: exercises.count)
            showResults = Array(repeating: false, count: exercises.count)
            updateProgress()
        }
        .onChange(of: currentIndex) { _ in
            updateProgress()
        }
    }
    
    private func updateProgress() {
        withAnimation(.easeInOut(duration: 0.5)) {
            progress = CGFloat(currentIndex) / CGFloat(max(1, exercises.count - 1))
        }
    }
    
    private func binding(for index: Int) -> Binding<Int?> {
        return Binding<Int?>(
            get: {
                guard index < selectedAnswers.count else { return nil }
                return selectedAnswers[index]
            },
            set: { newValue in
                guard index < selectedAnswers.count else { return }
                selectedAnswers[index] = newValue
            }
        )
    }

    private func showResultBinding(for index: Int) -> Binding<Bool> {
        return Binding<Bool>(
            get: {
                guard index < showResults.count else { return false }
                return showResults[index]
            },
            set: { newValue in
                guard index < showResults.count else { return }
                showResults[index] = newValue
            }
        )
    }
    
    private var modernHeaderView: some View {
        VStack(spacing: 16) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(.ultraThinMaterial)
                            .opacity(0.3)
                    )
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Text("Practice")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                    
                    if !exercises.isEmpty {
                        Text("\(currentIndex + 1) of \(exercises.count)")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                
                Spacer()
                
                Button(action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        if currentIndex < selectedAnswers.count {
                            selectedAnswers[currentIndex] = nil
                        }
                        if currentIndex < showResults.count {
                            showResults[currentIndex] = false
                        }
                    }
                }) {
                    Text("Reset")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .opacity(0.3)
                        )
                }
            }
            .padding(.horizontal, 20)
            
            // Progress bar with glow effect
            if !exercises.isEmpty {
                VStack(spacing: 8) {
                    ZStack(alignment: .leading) {
                        RoundedRectangle(cornerRadius: 10)
                            .fill(.ultraThinMaterial)
                            .opacity(0.3)
                            .frame(height: 8)
                        
                        RoundedRectangle(cornerRadius: 10)
                            .fill(
                                LinearGradient(
                                    colors: [.yellow, .orange, .pink],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: max(8, progress * 280), height: 8)
                            .shadow(color: .orange.opacity(0.5), radius: 4, x: 0, y: 0)
                    }
                    .frame(width: 280)
                }
            }
        }
        .padding(.top, 10)
        .padding(.bottom, 20)
    }
    
    private var modernEmptyStateView: some View {
        VStack(spacing: 24) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.orange.opacity(0.3), .pink.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .shadow(color: .orange.opacity(0.3), radius: 20, x: 0, y: 10)
                
                Image(systemName: "pencil.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.white)
            }
            
            VStack(spacing: 12) {
                Text("No Exercises")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.white)
                
                Text("Practice exercises will be available soon")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var modernBottomControls: some View {
        HStack(spacing: 20) {
            if !exercises.isEmpty {
                Button(action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        currentIndex = max(0, currentIndex - 1)
                    }
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                        Text("Previous")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(currentIndex > 0 ? .white : .white.opacity(0.5))
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(.ultraThinMaterial)
                            .opacity(currentIndex > 0 ? 0.4 : 0.2)
                    )
                    .shadow(color: currentIndex > 0 ? .black.opacity(0.1) : .clear, radius: 5, x: 0, y: 2)
                }
                .disabled(currentIndex <= 0)
                
                Button(action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        currentIndex = min(exercises.count - 1, currentIndex + 1)
                    }
                }) {
                    HStack(spacing: 8) {
                        Text("Next")
                        Image(systemName: "chevron.right")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(currentIndex < exercises.count - 1 ? .white : .white.opacity(0.5))
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(.ultraThinMaterial)
                            .opacity(currentIndex < exercises.count - 1 ? 0.4 : 0.2)
                    )
                    .shadow(color: currentIndex < exercises.count - 1 ? .black.opacity(0.1) : .clear, radius: 5, x: 0, y: 2)
                }
                .disabled(currentIndex >= exercises.count - 1)
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 30)
    }
}

struct ModernExerciseCard: View {
    let exercise: LessonExerciseItem
    let exerciseIndex: Int
    @Binding var selectedAnswer: Int?
    @Binding var showResult: Bool
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Card header with exercise type
                VStack(spacing: 16) {
                    HStack {
                        Text("Exercise \(exerciseIndex + 1)")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.orange)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(.orange.opacity(0.2))
                            )
                        
                        Spacer()
                        
                        Text("100 pts")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.orange)
                    }
                    
                    Text(exercise.type.uppercased())
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                        .tracking(2)
                }
                .padding(.horizontal, 24)
                .padding(.top, 20)
                
                // Main exercise content card
                VStack(spacing: 20) {
                    // Question with audio button
                    HStack(spacing: 12) {
                        // Extract Tamil text from question (format: "English\nTamil")
                        let questionLines = exercise.question.components(separatedBy: "\n")
                        let tamilText = questionLines.count > 1 ? questionLines[1] : exercise.question
                        
                        EnhancedAudioButton(
                            text: tamilText,
                            audioURL: exercise.questionAudioURL,
                            context: "exercise",
                            size: 36,
                            color: .orange
                        )
                        
                        Text(exercise.question)
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.leading)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 24)
                    
                    // Options with sexy animations
                    LazyVStack(spacing: 12) {
                        ForEach(Array(exercise.options.enumerated()), id: \.offset) { index, option in
                            OptionCard(
                                option: option,
                                index: index,
                                isSelected: selectedAnswer == index,
                                isCorrect: selectedAnswer == index && index == exercise.correctAnswer,
                                isWrong: selectedAnswer == index && index != exercise.correctAnswer,
                                showResult: showResult
                            ) {
                                if selectedAnswer == nil {
                                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                                        selectedAnswer = index
                                        showResult = true
                                    }
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 24)
                }
                .padding(.vertical, 24)
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(.ultraThinMaterial)
                        .opacity(0.1)
                        .background(
                            RoundedRectangle(cornerRadius: 24)
                                .stroke(.white.opacity(0.2), lineWidth: 1)
                        )
                )
                .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
                .padding(.horizontal, 20)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6)) {
                animationOffset = 0
            }
        }
    }
}

struct OptionCard: View {
    let option: String
    let index: Int
    let isSelected: Bool
    let isCorrect: Bool
    let isWrong: Bool
    let showResult: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                Text("\(index + 1).")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(textColor)
                    .frame(width: 24)
                
                Text(option)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(textColor)
                    .multilineTextAlignment(.leading)
                
                Spacer()
                
                if showResult && isSelected {
                    Image(systemName: isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(isCorrect ? .green : .red)
                        .scaleEffect(showResult ? 1.0 : 0.1)
                        .animation(.spring(response: 0.5, dampingFraction: 0.6), value: showResult)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(backgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(borderColor, lineWidth: 2)
                    )
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .shadow(color: shadowColor, radius: isSelected ? 8 : 4, x: 0, y: isSelected ? 4 : 2)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.5, dampingFraction: 0.7), value: isSelected)
    }
    
    private var backgroundColor: Color {
        if showResult && isSelected {
            return isCorrect ? .green.opacity(0.2) : .red.opacity(0.2)
        } else if isSelected {
            return .white.opacity(0.2)
        } else {
            return .white.opacity(0.1)
        }
    }
    
    private var borderColor: Color {
        if showResult && isSelected {
            return isCorrect ? .green : .red
        } else if isSelected {
            return .orange
        } else {
            return .white.opacity(0.3)
        }
    }
    
    private var textColor: Color {
        return .white
    }
    
    private var shadowColor: Color {
        if showResult && isSelected {
            return isCorrect ? .green.opacity(0.3) : .red.opacity(0.3)
        } else if isSelected {
            return .orange.opacity(0.3)
        } else {
            return .black.opacity(0.1)
        }
    }
}

struct ParticleView: View {
    @State private var particles: [Particle] = []
    
    var body: some View {
        ZStack {
            ForEach(particles, id: \.id) { particle in
                Circle()
                    .fill(.white.opacity(0.1))
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .animation(.easeInOut(duration: Double.random(in: 2...5)).repeatForever(autoreverses: true), value: particle.position)
            }
        }
        .onAppear {
            generateParticles()
        }
    }
    
    private func generateParticles() {
        particles = (0..<50).map { _ in
            Particle(
                id: UUID(),
                position: CGPoint(
                    x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                    y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                ),
                size: CGFloat.random(in: 2...6)
            )
        }
    }
}

struct Particle {
    let id: UUID
    let position: CGPoint
    let size: CGFloat
}

#Preview {
    let sampleExercises = [
        LessonExerciseItem(
            type: "multiple_choice",
            question: "What is the Tamil word for 'dog'?",
            options: ["நாய்", "பூனை", "யானை", "சிங்கம்"],
            correctAnswer: 0,
            explanation: "நாய் means dog in Tamil",
            points: 10,
            optionsPronunciations: ["naay", "poonai", "yaanai", "singam"]
        ),
        LessonExerciseItem(
            type: "fill_in_blank",
            question: "Complete: பறவை ______ (flies)",
            options: ["பறக்கிறது", "நடக்கிறது", "ஓடுகிறது", "நீந்துகிறது"],
            correctAnswer: 0,
            explanation: "Birds fly, so பறக்கிறது is correct",
            points: 15,
            optionsPronunciations: ["parakkiRadhu", "nadakkiRadhu", "odukiRadhu", "neeendhukiRadhu"]
        )
    ]
    
    ExerciseDetailView(exercises: sampleExercises)
}
