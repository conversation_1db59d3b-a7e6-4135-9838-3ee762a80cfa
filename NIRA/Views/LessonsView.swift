//
//  LessonsView.swift
//  NIRA
//

import SwiftUI

struct LessonsView: View {
    @State private var lessons: [SupabaseLesson] = []
    @State private var selectedLevel: String = "All"
    @State private var selectedLesson: SupabaseLesson?
    @State private var showingLessonDetail = false
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var debugInfo: String = "Loading..."
    
    // Computed property to filter lessons based on selected level
    private var filteredLessons: [SupabaseLesson] {
        if selectedLevel == "All" {
            return lessons
        } else {
            let filtered = lessons.filter { lesson in
                let difficultyLevel = lesson.difficultyLevel ?? 1
                let matches: Bo<PERSON>
                switch selectedLevel {
                case "Beginner":
                    matches = difficultyLevel == 1  // A1
                case "Elementary":
                    matches = difficultyLevel == 2  // A2
                case "Intermediate":
                    matches = difficultyLevel == 3 || difficultyLevel == 4  // B1, B2
                case "Advanced":
                    matches = difficultyLevel == 5  // C1
                case "Mastery":
                    matches = difficultyLevel == 6  // C2
                default:
                    matches = true
                }
                return matches
            }
            print("🔍 Filter '\(selectedLevel)': \(filtered.count) of \(lessons.count) lessons")
            return filtered
        }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 16) {
                    Text("\(userPreferences.selectedLanguage.displayName) Lessons")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Master \(userPreferences.selectedLanguage.displayName) through structured lessons")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal)
                
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(["All", "Beginner", "Elementary", "Intermediate", "Advanced", "Mastery"], id: \.self) { level in
                            Button(action: { 
                                print("🔘 Filter button tapped: \(level)")
                                selectedLevel = level 
                            }) {
                                Text(level)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(selectedLevel == level ? .white : .primary)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 20)
                                            .fill(selectedLevel == level ? Color.orange : Color(.systemGray5))
                                    )
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Debug info
                HStack {
                    Text("Filter: \(selectedLevel)")
                    Spacer()
                    Text("Showing: \(filteredLessons.count) of \(lessons.count)")
                }
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.horizontal)
                
                // Debug info
                Text("Filter: '\(selectedLevel)' | Total: \(lessons.count) | Filtered: \(filteredLessons.count)")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.horizontal)
                
                LazyVStack(spacing: 16) {
                    ForEach(filteredLessons) { lesson in
                        DiscoverStyleLessonCard(lesson: lesson) {
                            selectedLesson = lesson
                            showingLessonDetail = true
                        }
                    }
                }
                .padding(.horizontal)
            }
            .padding(.vertical)
        }
        .task {
            await loadLessons()
        }
        .onChange(of: userPreferences.selectedLanguage) { _ in
            Task {
                await loadLessons()
            }
        }
        .sheet(isPresented: $showingLessonDetail) {
            if let selectedLesson = selectedLesson {
                LessonDetailView(lesson: selectedLesson)
            }
        }
    }
    
    private func loadLessons() async {
        do {
            let supabaseClient = NIRASupabaseClient.shared
            // Load all lessons for client-side filtering
            let fetchedLessons = try await supabaseClient.getLessons(language: userPreferences.selectedLanguage.rawValue)
            await MainActor.run {
                self.lessons = fetchedLessons
            }
        } catch {
            print("Error loading lessons: \(error)")
        }
    }
}

struct DiscoverStyleLessonCard: View {
    let lesson: SupabaseLesson
    let onTap: () -> Void
    @StateObject private var userPreferences = UserPreferencesService.shared
    
    // Dynamic vibrant gradient based on lesson content
    private var cardVariant: CardVariant {
        let title = lesson.title.lowercased()
        if title.contains("greeting") || title.contains("introduction") {
            return .blue
        } else if title.contains("number") || title.contains("color") {
            return .purple
        } else if title.contains("family") || title.contains("relation") {
            return .green
        } else if title.contains("time") || title.contains("calendar") {
            return .orange
        } else {
            return .pink
        }
    }
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(cardVariant.gradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)
                
                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(cardVariant.gradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))
                    
                    VStack(alignment: .leading, spacing: 12) {
                        // Header with lesson info
                        HStack {
                            HStack(spacing: 8) {
                                Image(systemName: "book.fill")
                                    .foregroundColor(cardVariant.primaryColor)
                                    .font(.title3)
                                
                                Text("Lesson")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(cardVariant.primaryColor)
                            }
                            
                            Spacer()
                            
                            HStack(spacing: 4) {
                                Image(systemName: "clock")
                                    .font(.caption)
                                Text("15 mins")
                                    .font(.caption)
                            }
                            .foregroundColor(.secondary)
                        }
                        
                        // Title and description
                        Text(lesson.title)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                        
                        Text(lesson.description ?? "Learn \(userPreferences.selectedLanguage.displayName) with this comprehensive lesson")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                        
                        // Metrics grid
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 12) {
                            MetricItem(
                                icon: "book.closed.fill",
                                label: "Vocabulary",
                                value: 25,
                                variant: cardVariant
                            )
                            
                            MetricItem(
                                icon: "graduationcap.fill",
                                label: "Grammar",
                                value: 5,
                                variant: cardVariant
                            )
                            
                            MetricItem(
                                icon: "message.circle.fill",
                                label: "Conversations",
                                value: 10,
                                variant: cardVariant
                            )
                            
                            MetricItem(
                                icon: "dumbbell.fill",
                                label: "Exercises",
                                value: 10,
                                variant: cardVariant
                            )
                        }
                    }
                    .padding(.horizontal, 4)
                }
                .padding(20)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(cardVariant.primaryColor.opacity(0.3), lineWidth: 1)
                    )
            )
            .shadow(color: cardVariant.primaryColor.opacity(0.2), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Views and Types

enum CardVariant {
    case blue, purple, green, orange, pink
    
    var primaryColor: Color {
        switch self {
        case .blue: return .blue
        case .purple: return .purple
        case .green: return .green
        case .orange: return .orange
        case .pink: return .pink
        }
    }
    
    var gradient: LinearGradient {
        switch self {
        case .blue:
            return LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .purple:
            return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .green:
            return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .orange:
            return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .pink:
            return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
}

struct MetricItem: View {
    let icon: String
    let label: String
    let value: Int
    let variant: CardVariant
    
    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(variant.gradient)
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                Text("\(value)")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6).opacity(0.6))
        )
    }
}