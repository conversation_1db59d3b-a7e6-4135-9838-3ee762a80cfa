import SwiftUI

// MARK: - Script Reader View

struct ScriptReaderView: View {
    let lesson: ReadingLesson
    let language: Language
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentReadingMode: ScriptReadingMode = .script
    @State private var fontSize: CGFloat = 24
    @State private var showingTransliteration = false
    @State private var showingTranslation = false
    @State private var readingProgress: Double = 0.0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Reading Controls
                readingControlsBar
                
                // Main Reading Content
                ScrollView {
                    VStack(alignment: .leading, spacing: 20) {
                        // Lesson Info
                        lessonInfoHeader
                        
                        // Reading Content
                        readingContentView
                        
                        // Progress Tracking
                        readingProgressView
                    }
                    .padding()
                }
                
                // Bottom Action Bar
                bottomActionBar
            }
            .navigationTitle("Reading Practice")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Complete") {
                        // TODO: Mark lesson as complete
                        dismiss()
                    }
                    .foregroundColor(.green)
                }
            }
        }
    }
    
    // MARK: - Views
    
    private var readingControlsBar: some View {
        HStack {
            // Font Size Controls
            HStack(spacing: 8) {
                Button {
                    fontSize = max(16, fontSize - 2)
                } label: {
                    Image(systemName: "textformat.size.smaller")
                        .font(.caption)
                }
                
                Text("\(Int(fontSize))pt")
                    .font(.caption)
                    .frame(width: 32)
                
                Button {
                    fontSize = min(32, fontSize + 2)
                } label: {
                    Image(systemName: "textformat.size.larger")
                        .font(.caption)
                }
            }
            .foregroundColor(.blue)
            
            Spacer()
            
            // Reading Mode Toggle
            Picker("Reading Mode", selection: $currentReadingMode) {
                ForEach(ScriptReadingMode.allCases, id: \.self) { mode in
                    Text(mode.displayName).tag(mode)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .frame(width: 200)
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    private var lessonInfoHeader: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(lesson.title)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Text(language.flag)
                    .font(.title)
            }
            
            Text(lesson.subtitle)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack {
                Label("\(lesson.estimatedTime) min", systemImage: "clock")
                    .font(.caption)
                    .foregroundColor(.blue)
                
                Spacer()
                
                Label("\(lesson.completionReward) points", systemImage: "star.fill")
                    .font(.caption)
                    .foregroundColor(.orange)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4)
        )
    }
    
    private var readingContentView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Main Script Content
            scriptContentSection
            
            // Optional Transliteration
            if showingTransliteration {
                transliterationSection
            }
            
            // Optional Translation
            if showingTranslation {
                translationSection
            }
        }
    }
    
    private var scriptContentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Script")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if currentReadingMode == .script {
                    Image(systemName: "eye.fill")
                        .foregroundColor(.blue)
                }
            }
            
            Text(lesson.scriptContent)
                .font(.system(size: fontSize))
                .lineSpacing(8)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(currentReadingMode == .script ? Color.blue.opacity(0.1) : Color(.systemGray6))
                )
                .onTapGesture {
                    // TODO: Add pronunciation audio
                }
        }
    }
    
    private var transliterationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Transliteration")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if currentReadingMode == .transliteration {
                    Image(systemName: "eye.fill")
                        .foregroundColor(.green)
                }
            }
            
            Text(lesson.transliteration)
                .font(.system(size: fontSize - 2))
                .italic()
                .lineSpacing(6)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(currentReadingMode == .transliteration ? Color.green.opacity(0.1) : Color(.systemGray6))
                )
        }
    }
    
    private var translationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Translation")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if currentReadingMode == .translation {
                    Image(systemName: "eye.fill")
                        .foregroundColor(.orange)
                }
            }
            
            Text(lesson.translation)
                .font(.system(size: fontSize - 4))
                .lineSpacing(6)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(currentReadingMode == .translation ? Color.orange.opacity(0.1) : Color(.systemGray6))
                )
        }
    }
    
    private var readingProgressView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Reading Progress")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                HStack {
                    Text("Completion")
                    Spacer()
                    Text("\(Int(readingProgress * 100))%")
                        .fontWeight(.medium)
                }
                .font(.subheadline)
                
                ProgressView(value: readingProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .onAppear {
            // Simulate reading progress
            withAnimation(.easeInOut(duration: 2.0)) {
                readingProgress = 0.7
            }
        }
    }
    
    private var bottomActionBar: some View {
        HStack(spacing: 16) {
            // Toggle Transliteration
            Button {
                withAnimation(.spring()) {
                    showingTransliteration.toggle()
                }
            } label: {
                VStack(spacing: 4) {
                    Image(systemName: showingTransliteration ? "textformat.abc.dottedunderline" : "textformat.abc")
                        .font(.title3)
                    Text("Romanized")
                        .font(.caption)
                }
                .foregroundColor(showingTransliteration ? .green : .secondary)
            }
            
            Spacer()
            
            // Audio Playback (TODO)
            Button {
                // TODO: Play audio pronunciation
            } label: {
                VStack(spacing: 4) {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.title3)
                    Text("Audio")
                        .font(.caption)
                }
                .foregroundColor(.blue)
            }
            
            Spacer()
            
            // Toggle Translation
            Button {
                withAnimation(.spring()) {
                    showingTranslation.toggle()
                }
            } label: {
                VStack(spacing: 4) {
                    Image(systemName: showingTranslation ? "translate.fill" : "translate")
                        .font(.title3)
                    Text("Translate")
                        .font(.caption)
                }
                .foregroundColor(showingTranslation ? .orange : .secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: -2)
    }
}

// MARK: - Supporting Types

enum ScriptReadingMode: CaseIterable {
    case script
    case transliteration
    case translation
    
    var displayName: String {
        switch self {
        case .script: return "Script"
        case .transliteration: return "Romanized"
        case .translation: return "English"
        }
    }
}

#Preview {
    ScriptReaderView(
        lesson: ReadingLesson(
            id: "sample",
            title: "Sample Tamil Reading",
            subtitle: "Basic Tamil text",
            level: .beginner,
            scriptContent: "நான் பள்ளிக்கு போகிறேன். என் வீடு பெரியது.",
            transliteration: "nān paḷḷikku pōkiṟēṉ. eṉ vīṭu periyatu.",
            translation: "I am going to school. My house is big.",
            difficulty: 2,
            estimatedTime: 15,
            completionReward: 75
        ),
        language: .tamil
    )
} 