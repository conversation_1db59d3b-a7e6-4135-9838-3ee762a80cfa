import SwiftUI
import PencilKit

// MARK: - Writing View for Script-Based Languages

struct WritingView: View {
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var writingLessons: [WritingLesson] = []
    @State private var selectedMode: WritingMode = .guided
    @State private var isLoading = true
    @State private var showingWritingPad = false
    @State private var selectedLesson: WritingLesson?
    
    let language: Language
    
    var body: some View {
        VStack(spacing: 0) {
            // Writing Header
            WritingHeader(
                language: language,
                selectedMode: $selectedMode
            )
            
            // Main Content
            if isLoading {
                loadingView
            } else {
                writingContentView
            }
        }
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.05), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .navigationBarHidden(true)
        .task {
            await loadWritingContent()
        }
        .sheet(isPresented: $showingWritingPad) {
            if let lesson = selectedLesson {
                WritingPadView(lesson: lesson, language: language)
            }
        }
    }
    
    // MARK: - Views
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading \(language.displayName) writing exercises...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var writingContentView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Writing Mode Info
                WritingModeInfoCard(mode: selectedMode, language: language)
                
                // Writing Lessons
                ForEach(writingLessons.filter { $0.mode == selectedMode }) { lesson in
                    WritingLessonCard(
                        lesson: lesson,
                        language: language,
                        onTap: {
                            selectedLesson = lesson
                            showingWritingPad = true
                        }
                    )
                }
                
                // Quick Practice Button
                QuickPracticeButton(language: language) {
                    // TODO: Open quick practice mode
                }
            }
            .padding()
        }
    }
    
    // MARK: - Data Loading
    
    private func loadWritingContent() async {
        isLoading = true
        defer { isLoading = false }
        
        // Mock data for Tamil - replace with actual content loading
        writingLessons = generateMockWritingLessons(for: language)
        
        // Artificial delay to show loading state
        try? await Task.sleep(nanoseconds: 1_000_000_000)
    }
    
    private func generateMockWritingLessons(for language: Language) -> [WritingLesson] {
        switch language {
        case .tamil:
            return [
                WritingLesson(
                    id: "tamil_vowels_guided",
                    title: "Tamil Vowels Practice",
                    subtitle: "Guided writing: அ, ஆ, இ, ஈ",
                    mode: .guided,
                    characters: ["அ", "ஆ", "இ", "ஈ"],
                    instructions: "Follow the stroke order shown on screen",
                    difficulty: 1,
                    estimatedTime: 15,
                    completionReward: 75
                ),
                WritingLesson(
                    id: "tamil_consonants_guided",
                    title: "Basic Consonants",
                    subtitle: "Guided writing: க், ங், ச், ஞ்",
                    mode: .guided,
                    characters: ["க்", "ங்", "ச்", "ஞ்"],
                    instructions: "Practice each consonant with proper stroke order",
                    difficulty: 2,
                    estimatedTime: 20,
                    completionReward: 100
                ),
                WritingLesson(
                    id: "tamil_vowels_freeform",
                    title: "Vowel Writing Test",
                    subtitle: "Write Tamil vowels from memory",
                    mode: .freeform,
                    characters: ["அ", "ஆ", "இ", "ஈ", "உ", "ஊ"],
                    instructions: "Write each vowel without guidance",
                    difficulty: 3,
                    estimatedTime: 25,
                    completionReward: 150
                )
            ]
        default:
            return [] // Other languages would have their own content
        }
    }
}

// MARK: - Writing Header

struct WritingHeader: View {
    let language: Language
    @Binding var selectedMode: WritingMode
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(language.displayName) Writing")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Practice \(language.displayName) script writing")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text(language.flag)
                    .font(.system(size: 32))
            }
            
            // Mode Selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(WritingMode.allCases, id: \.self) { mode in
                        WritingModeChip(
                            mode: mode,
                            isSelected: selectedMode == mode,
                            action: {
                                withAnimation(.spring()) {
                                    selectedMode = mode
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// MARK: - Writing Mode Chip

struct WritingModeChip: View {
    let mode: WritingMode
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: mode.icon)
                    .font(.caption)
                
                Text(mode.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? Color.green : Color(.secondarySystemBackground))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Writing Mode Info Card

struct WritingModeInfoCard: View {
    let mode: WritingMode
    let language: Language
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: mode.icon)
                    .font(.title2)
                    .foregroundColor(.green)
                
                Text(mode.displayName)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            Text(mode.description)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Writing Lesson Card

struct WritingLessonCard: View {
    let lesson: WritingLesson
    let language: Language
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(lesson.subtitle)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        Text("\(lesson.estimatedTime) min")
                            .font(.caption)
                            .foregroundColor(.green)
                        
                        Text("⭐ \(lesson.completionReward)")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                // Character Preview
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(lesson.characters, id: \.self) { character in
                            Text(character)
                                .font(.title)
                                .foregroundColor(.primary)
                                .padding(8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(.secondarySystemBackground))
                                )
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Instructions
                Text(lesson.instructions)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
                
                // Difficulty Indicator
                HStack {
                    ForEach(1...5, id: \.self) { level in
                        Image(systemName: level <= lesson.difficulty ? "star.fill" : "star")
                            .font(.caption)
                            .foregroundColor(level <= lesson.difficulty ? .orange : .gray)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Quick Practice Button

struct QuickPracticeButton: View {
    let language: Language
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: "pencil.and.outline")
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Quick Practice")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("5-minute \(language.displayName) writing session")
                        .font(.subheadline)
                        .opacity(0.8)
                }
                
                Spacer()
                
                Image(systemName: "arrow.right.circle.fill")
                    .font(.title2)
            }
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [.green, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Types

enum WritingMode: CaseIterable {
    case guided
    case freeform
    case assessment
    
    var displayName: String {
        switch self {
        case .guided: return "Guided"
        case .freeform: return "Freeform"
        case .assessment: return "Assessment"
        }
    }
    
    var icon: String {
        switch self {
        case .guided: return "hand.draw"
        case .freeform: return "pencil"
        case .assessment: return "checkmark.circle"
        }
    }
    
    var description: String {
        switch self {
        case .guided:
            return "Follow step-by-step guidance with stroke order demonstrations and real-time feedback."
        case .freeform:
            return "Practice writing freely with character recognition and accuracy scoring."
        case .assessment:
            return "Test your writing skills with timed challenges and comprehensive evaluation."
        }
    }
}

struct WritingLesson: Identifiable {
    let id: String
    let title: String
    let subtitle: String
    let mode: WritingMode
    let characters: [String]
    let instructions: String
    let difficulty: Int // 1-5 stars
    let estimatedTime: Int // minutes
    let completionReward: Int // points
}

// MARK: - Writing Pad View (Placeholder for full implementation)

struct WritingPadView: View {
    let lesson: WritingLesson
    let language: Language
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Writing Practice: \(lesson.title)")
                    .font(.headline)
                    .padding()
                
                // TODO: Implement PencilKit canvas and character recognition
                Rectangle()
                    .fill(Color(.systemGray6))
                    .overlay(
                        Text("Writing Canvas\n(PencilKit Integration)")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    )
                    .padding()
                
                Spacer()
            }
            .navigationTitle("Writing Practice")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    WritingView(language: .tamil)
} 