import SwiftUI

// MARK: - Reading View for Script-Based Languages

struct ReadingView: View {
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var readingLessons: [ReadingLesson] = []
    @State private var selectedLevel: ReadingLevel = .beginner
    @State private var isLoading = true
    @State private var showingReader = false
    @State private var selectedLesson: ReadingLesson?
    
    let language: Language
    
    var body: some View {
        VStack(spacing: 0) {
            // Reading Header
            ReadingHeader(
                language: language,
                selectedLevel: $selectedLevel
            )
            
            // Main Content
            if isLoading {
                loadingView
            } else {
                readingContentView
            }
        }
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.05), Color.purple.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .navigationBarHidden(true)
        .task {
            await loadReadingContent()
        }
        .sheet(isPresented: $showingReader) {
            if let lesson = selectedLesson {
                ScriptReaderView(lesson: lesson, language: language)
            }
        }
    }
    
    // MARK: - Views
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading \(language.displayName) reading content...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var readingContentView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Script Introduction (for beginners)
                if selectedLevel == .beginner {
                    ScriptIntroductionCard(language: language)
                }
                
                // Reading Lessons
                ForEach(readingLessons.filter { $0.level == selectedLevel }) { lesson in
                    ReadingLessonCard(
                        lesson: lesson,
                        language: language,
                        onTap: {
                            selectedLesson = lesson
                            showingReader = true
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - Data Loading
    
    private func loadReadingContent() async {
        isLoading = true
        defer { isLoading = false }
        
        // Mock data for Tamil - replace with actual content loading
        readingLessons = generateMockReadingLessons(for: language)
        
        // Artificial delay to show loading state
        try? await Task.sleep(nanoseconds: 1_000_000_000)
    }
    
    private func generateMockReadingLessons(for language: Language) -> [ReadingLesson] {
        switch language {
        case .tamil:
            return [
                ReadingLesson(
                    id: "tamil_basic_letters",
                    title: "Tamil Letters (அ - ஃ)",
                    subtitle: "Basic vowels and consonants",
                    level: .beginner,
                    scriptContent: "அ ஆ இ ஈ உ ஊ எ ஏ ஐ ஒ ஓ ஔ",
                    transliteration: "a ā i ī u ū e ē ai o ō au",
                    translation: "Tamil vowels: a, aa, i, ii, u, uu, e, ee, ai, o, oo, au",
                    difficulty: 1,
                    estimatedTime: 10,
                    completionReward: 50
                ),
                ReadingLesson(
                    id: "tamil_simple_words",
                    title: "Simple Tamil Words",
                    subtitle: "Common everyday words",
                    level: .beginner,
                    scriptContent: "நான் - வீடு - பள்ளி - நீர் - உணவு",
                    transliteration: "nān - vīṭu - paḷḷi - nīr - uṇavu",
                    translation: "I - house - school - water - food",
                    difficulty: 2,
                    estimatedTime: 15,
                    completionReward: 75
                ),
                ReadingLesson(
                    id: "tamil_sentences",
                    title: "Simple Sentences",
                    subtitle: "Basic sentence structure",
                    level: .intermediate,
                    scriptContent: "நான் பள்ளிக்கு போகிறேன். என் வீடு பெரியது.",
                    transliteration: "nān paḷḷikku pōkiṟēṉ. eṉ vīṭu periyatu.",
                    translation: "I am going to school. My house is big.",
                    difficulty: 3,
                    estimatedTime: 20,
                    completionReward: 100
                )
            ]
        default:
            return [] // Other languages would have their own content
        }
    }
}

// MARK: - Reading Header

struct ReadingHeader: View {
    let language: Language
    @Binding var selectedLevel: ReadingLevel
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(language.displayName) Reading")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Master \(language.displayName) script and reading")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text(language.flag)
                    .font(.system(size: 32))
            }
            
            // Level Selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(ReadingLevel.allCases, id: \.self) { level in
                        ReadingLevelChip(
                            level: level,
                            isSelected: selectedLevel == level,
                            action: {
                                withAnimation(.spring()) {
                                    selectedLevel = level
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// MARK: - Reading Level Chip

struct ReadingLevelChip: View {
    let level: ReadingLevel
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(level.displayName)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.blue : Color(.secondarySystemBackground))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Script Introduction Card

struct ScriptIntroductionCard: View {
    let language: Language
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "book.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                Text("Learn \(language.displayName) Script")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            Text(getScriptDescription(for: language))
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
            
            Button("Start Script Introduction") {
                // TODO: Open script introduction lesson
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue.gradient)
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func getScriptDescription(for language: Language) -> String {
        switch language {
        case .tamil:
            return "Tamil script is an ancient writing system with 12 vowels, 18 consonants, and one special character (ஃ). It's written from left to right and doesn't use upper/lower case distinctions."
        case .hindi:
            return "Hindi uses the Devanagari script, written from left to right with a horizontal line connecting letters. It has 11 vowels and 33 consonants."
        case .japanese:
            return "Japanese uses three writing systems: Hiragana (phonetic), Katakana (foreign words), and Kanji (Chinese characters)."
        case .korean:
            return "Korean uses Hangul, a logical alphabet where letters are grouped into syllable blocks. It has 14 consonants and 10 vowels."
        case .arabic:
            return "Arabic script is written from right to left and uses connected letters that change shape depending on their position in a word."
        default:
            return "Learn the unique writing system and reading patterns of \(language.displayName)."
        }
    }
}

// MARK: - Reading Lesson Card

struct ReadingLessonCard: View {
    let lesson: ReadingLesson
    let language: Language
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(lesson.subtitle)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        Text("\(lesson.estimatedTime) min")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        Text("⭐ \(lesson.completionReward)")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                // Script Preview
                Text(lesson.scriptContent)
                    .font(.title2)
                    .foregroundColor(.primary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.secondarySystemBackground))
                    )
                
                // Difficulty Indicator
                HStack {
                    ForEach(1...5, id: \.self) { level in
                        Image(systemName: level <= lesson.difficulty ? "star.fill" : "star")
                            .font(.caption)
                            .foregroundColor(level <= lesson.difficulty ? .orange : .gray)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Types

enum ReadingLevel: CaseIterable {
    case beginner
    case intermediate
    case advanced
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
}

struct ReadingLesson: Identifiable {
    let id: String
    let title: String
    let subtitle: String
    let level: ReadingLevel
    let scriptContent: String
    let transliteration: String
    let translation: String
    let difficulty: Int // 1-5 stars
    let estimatedTime: Int // minutes
    let completionReward: Int // points
}

#Preview {
    ReadingView(language: .tamil)
} 