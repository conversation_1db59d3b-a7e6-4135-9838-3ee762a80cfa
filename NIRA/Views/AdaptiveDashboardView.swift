import SwiftUI
import Charts

struct AdaptiveDashboardView: View {
    @StateObject private var analyticsService = LearningAnalyticsService.shared
    @State private var selectedTimeframe: LearningAnalyticsSummary.AnalyticsTimeframe = .weekly
    @State private var analyticsSummary: LearningAnalyticsSummary?
    @State private var showingAchievements = false
    @State private var showingDetailedAnalytics = false

    // Sample user data - in real app this would come from authentication
    private let userId = UUID()
    private let languageId = UUID()

    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            ModernDashboardHeader(
                selectedLanguage: "Tamil", // This could be dynamic based on user preferences
                userName: "Alex",
                currentStreak: 7 // This could be dynamic based on user data
            )

            // Main Content
            ScrollView {
                VStack(spacing: 24) {
                    // Header with timeframe selector
                    dashboardHeader

                    // Key metrics overview
                    keyMetricsSection

                    // Progress visualization
                    progressVisualizationSection

                    // Learning insights
                    learningInsightsSection

                    // Achievements showcase
                    achievementsSection

                    // Personalized recommendations
                    recommendationsSection

                    // Recent activity
                    recentActivitySection
                }
                .padding()
            }
            .background(
                LinearGradient(
                    colors: [Color.niraPrimary.opacity(0.05), Color.clear],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .background(Color(.systemGroupedBackground))
        .navigationBarHidden(true)
        .task {
            await loadDashboardData()
        }
        .refreshable {
            await loadDashboardData()
        }
        .sheet(isPresented: $showingAchievements) {
            AchievementsDetailView()
        }
        .sheet(isPresented: $showingDetailedAnalytics) {
            DetailedAnalyticsView(summary: analyticsSummary)
        }
    }

    // MARK: - Dashboard Sections

    private var dashboardHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Your Learning Journey")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Track your progress and insights")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button(action: { showingDetailedAnalytics = true }) {
                    Image(systemName: "chart.bar.xaxis")
                        .font(.title2)
                        .foregroundColor(.niraPrimary)
                }
            }

            // Timeframe selector
            Picker("Timeframe", selection: $selectedTimeframe) {
                Text("Daily").tag(LearningAnalyticsSummary.AnalyticsTimeframe.daily)
                Text("Weekly").tag(LearningAnalyticsSummary.AnalyticsTimeframe.weekly)
                Text("Monthly").tag(LearningAnalyticsSummary.AnalyticsTimeframe.monthly)
                Text("All Time").tag(LearningAnalyticsSummary.AnalyticsTimeframe.allTime)
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: selectedTimeframe) { _, _ in
                Task {
                    await loadAnalyticsSummary()
                }
            }
        }
    }

    private var keyMetricsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Key Metrics")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                MetricCard(
                    title: "Lessons Completed",
                    value: "\(analyticsSummary?.totalLessonsCompleted ?? 0)",
                    icon: "book.circle.fill",
                    color: .niraPrimary,
                    trend: .up,
                    trendValue: "+3 this week"
                )

                MetricCard(
                    title: "Study Time",
                    value: formatTime(analyticsSummary?.totalTimeSpent ?? 0),
                    icon: "clock.circle.fill",
                    color: .niraSecondary,
                    trend: .up,
                    trendValue: "+2h this week"
                )

                MetricCard(
                    title: "Accuracy",
                    value: "\(Int(analyticsSummary?.averageAccuracy ?? 0))%",
                    icon: "target",
                    color: .niraSuccess,
                    trend: .up,
                    trendValue: "+5% improvement"
                )

                MetricCard(
                    title: "Current Streak",
                    value: "\(analyticsSummary?.currentStreak ?? 0)",
                    icon: "flame.circle.fill",
                    color: .orange,
                    trend: .stable,
                    trendValue: "Keep it up!"
                )
            }
        }
    }

    private var progressVisualizationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Progress Overview")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 20) {
                // CEFR Level Progress
                CEFRProgressView(progress: analyticsSummary?.cefrLevelProgress ?? [:])

                // Weekly activity chart
                WeeklyActivityChart(summary: analyticsSummary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .niraPrimary.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    private var learningInsightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Learning Insights")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                if let profile = analyticsService.learningProfile {
                    InsightCard(
                        icon: "brain.head.profile",
                        title: "Optimal Study Time",
                        description: profile.optimalStudyTimes?.first.map {
                            "\(formatHour($0.hour)) is your peak learning time"
                        } ?? "We're analyzing your patterns",
                        color: .niraPrimary
                    )

                    if let strengths = profile.strengthAreas, !strengths.isEmpty {
                        InsightCard(
                            icon: "star.circle.fill",
                            title: "Your Strengths",
                            description: "You excel at: \(strengths.joined(separator: ", "))",
                            color: .niraSuccess
                        )
                    }

                    if let weaknesses = profile.weaknessAreas, !weaknesses.isEmpty {
                        InsightCard(
                            icon: "target",
                            title: "Focus Areas",
                            description: "Practice more: \(weaknesses.joined(separator: ", "))",
                            color: .niraWarning
                        )
                    }

                    if let velocity = profile.learningVelocity {
                        InsightCard(
                            icon: "speedometer",
                            title: "Learning Pace",
                            description: String(format: "%.1f lessons per week", velocity),
                            color: .niraInfo
                        )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .niraPrimary.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    private var achievementsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Achievements")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("View All") {
                    showingAchievements = true
                }
                .font(.subheadline)
                .foregroundColor(.niraPrimary)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(analyticsService.userAchievements.prefix(5), id: \.id) { userAchievement in
                        AchievementBadge(userAchievement: userAchievement)
                    }

                    if analyticsService.userAchievements.isEmpty {
                        EmptyAchievementCard()
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private var recommendationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recommended for You")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                ForEach(analyticsService.recommendations.prefix(3), id: \.id) { recommendation in
                                            AdaptiveRecommendationCard(recommendation: recommendation)
                }

                if analyticsService.recommendations.isEmpty {
                    EmptyRecommendationCard()
                }
            }
        }
    }

    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Activity")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                ForEach(Array(analyticsService.userProgress.values.suffix(5)), id: \.id) { progress in
                    LearningActivityRow(progress: progress)
                }

                if analyticsService.userProgress.isEmpty {
                    Text("No recent activity")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .niraPrimary.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    // MARK: - Helper Methods

    private func loadDashboardData() async {
        await analyticsService.loadAchievements()
        await loadAnalyticsSummary()
    }

    private func loadAnalyticsSummary() async {
        analyticsSummary = await analyticsService.generateAnalyticsSummary(
            userId: userId,
            languageId: languageId,
            timeframe: selectedTimeframe
        )
    }

    private func formatTime(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    private func formatHour(_ hour: Int) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "ha"
        let date = Calendar.current.date(bySettingHour: hour, minute: 0, second: 0, of: Date()) ?? Date()
        return formatter.string(from: date)
    }
}

// MARK: - Supporting Views

struct MetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: MetricTrendDirection
    let trendValue: String

    enum MetricTrendDirection {
        case up, down, stable

        var icon: String {
            switch self {
            case .up: return "arrow.up.right"
            case .down: return "arrow.down.right"
            case .stable: return "minus"
            }
        }

        var color: Color {
            switch self {
            case .up: return .green
            case .down: return .red
            case .stable: return .gray
            }
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Spacer()

                HStack(spacing: 4) {
                    Image(systemName: trend.icon)
                        .font(.caption)
                        .foregroundColor(trend.color)

                    Text(trendValue)
                        .font(.caption)
                        .foregroundColor(trend.color)
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct CEFRProgressView: View {
    let progress: [String: Double]

    private let cefrLevels = ["A1", "A2", "B1", "B2", "C1", "C2"]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("CEFR Level Progress")
                .font(.subheadline)
                .fontWeight(.semibold)

            VStack(spacing: 8) {
                ForEach(cefrLevels, id: \.self) { level in
                    HStack {
                        Text(level)
                            .font(.caption)
                            .fontWeight(.medium)
                            .frame(width: 30, alignment: .leading)

                        ProgressView(value: progress[level] ?? 0.0)
                            .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
                            .scaleEffect(x: 1, y: 1.5, anchor: .center)

                        Text("\(Int((progress[level] ?? 0.0) * 100))%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40, alignment: .trailing)
                    }
                }
            }
        }
    }
}

struct WeeklyActivityChart: View {
    let summary: LearningAnalyticsSummary?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Weekly Activity")
                .font(.subheadline)
                .fontWeight(.semibold)

            // Placeholder for chart - in real implementation, use Swift Charts
            HStack(spacing: 8) {
                ForEach(0..<7) { day in
                    VStack(spacing: 4) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.niraPrimary.opacity(Double.random(in: 0.3...1.0)))
                            .frame(width: 20, height: CGFloat.random(in: 20...60))

                        Text(dayAbbreviation(for: day))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(maxWidth: .infinity)
        }
    }

    private func dayAbbreviation(for day: Int) -> String {
        let days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        return days[day]
    }
}

struct InsightCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct AchievementBadge: View {
    let userAchievement: UserLearningAchievement

    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(Color.niraPrimary.gradient)
                    .frame(width: 60, height: 60)

                Text("🏆")
                    .font(.title2)
            }

            VStack(spacing: 2) {
                Text("Achievement")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(userAchievement.earnedAt, style: .date)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(width: 100)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .niraPrimary.opacity(0.2), radius: 4, x: 0, y: 2)
        )
    }
}

struct EmptyAchievementCard: View {
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: "star.circle")
                .font(.title)
                .foregroundColor(.gray)

            Text("Start learning to unlock achievements!")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(width: 120, height: 100)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct AdaptiveRecommendationCard: View {
    let recommendation: ContentRecommendation

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "lightbulb.circle.fill")
                .font(.title2)
                .foregroundColor(.niraPrimary)

            VStack(alignment: .leading, spacing: 4) {
                Text(recommendation.contentType.capitalized)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(recommendation.recommendationReason)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()

            VStack(spacing: 4) {
                Text("\(Int(recommendation.confidenceScore * 100))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraPrimary)

                Text("match")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.niraPrimary.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct EmptyRecommendationCard: View {
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "brain.head.profile")
                .font(.title2)
                .foregroundColor(.gray)

            VStack(alignment: .leading, spacing: 4) {
                Text("AI is learning about you")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("Complete more lessons to get personalized recommendations")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct LearningActivityRow: View {
    let progress: AdaptiveUserProgress

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: progress.completionStatus.icon)
                .font(.title3)
                .foregroundColor(progress.completionStatus.color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text("Lesson Completed")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text("\(progress.completionStatus.displayName) • \(formatTime(progress.timeSpentSeconds))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if let accuracy = progress.accuracyScore {
                Text("\(Int(accuracy))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraPrimary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.niraPrimary.opacity(0.1))
                    )
            }
        }
        .padding(.vertical, 8)
    }

    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        return "\(minutes)m"
    }
}

// MARK: - Detail Views

struct AchievementsDetailView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var analyticsService = LearningAnalyticsService.shared

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    ForEach(analyticsService.achievements, id: \.id) { achievement in
                        AchievementDetailCard(
                            achievement: achievement,
                            isEarned: analyticsService.userAchievements.contains { $0.achievementId == achievement.id }
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("Achievements")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct AchievementDetailCard: View {
    let achievement: LearningAchievement
    let isEarned: Bool

    var body: some View {
        VStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(isEarned ? achievement.difficulty.color.gradient : Color.gray.opacity(0.3).gradient)
                    .frame(width: 60, height: 60)

                Text(achievement.iconName ?? "🏆")
                    .font(.title2)
                    .opacity(isEarned ? 1.0 : 0.5)
            }

            VStack(spacing: 4) {
                Text(achievement.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(isEarned ? .primary : .secondary)
                    .multilineTextAlignment(.center)

                Text(achievement.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)

                Text("\(achievement.pointsReward) pts")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(achievement.difficulty.color)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(achievement.difficulty.color.opacity(0.2))
                    )
            }
        }
        .padding()
        .frame(height: 180)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isEarned ? achievement.difficulty.color.opacity(0.5) : Color.gray.opacity(0.3), lineWidth: 2)
                )
                .shadow(color: isEarned ? achievement.difficulty.color.opacity(0.3) : .clear, radius: 8, x: 0, y: 4)
        )
        .scaleEffect(isEarned ? 1.0 : 0.95)
        .opacity(isEarned ? 1.0 : 0.7)
    }
}

struct DetailedAnalyticsView: View {
    let summary: LearningAnalyticsSummary?
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    if let summary = summary {
                        // Detailed charts and analytics would go here
                        Text("Detailed analytics for \(summary.timeframe.rawValue)")
                            .font(.headline)

                        // This would contain more sophisticated charts and insights
                    } else {
                        Text("No analytics data available")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
            }
            .navigationTitle("Detailed Analytics")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AdaptiveDashboardView()
}