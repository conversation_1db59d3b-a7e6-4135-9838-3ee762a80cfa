import SwiftUI

struct EnhancedLessonsView: View {
    @State private var selectedSubTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Clean Header with Language Selector
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Learn")
                            .font(.system(size: 24, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                        Text("Discover language content")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    RegionalLanguageSelector()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                
                // Sub-tab picker
                Picker("Content Type", selection: $selectedSubTab) {
                    Text("Lessons").tag(0)
                    Text("Discover").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal, 16)
                .padding(.bottom, 8)
                
                // Content based on selected sub-tab
                TabView(selection: $selectedSubTab) {
                    // Learn tab: Existing lessons functionality
                    LessonsView()
                        .tag(0)
                    
                    // Discover tab: Cultural content
                    DiscoverContentView()
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationBarHidden(true)
        }
    }
}

struct DiscoverContentView: View {
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Cultural Content Header
                VStack(alignment: .leading, spacing: 8) {
                    Text("Discover Tamil Culture")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Explore the rich heritage and traditions")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Enhanced Cultural Cards
                EnhancedCulturalCard(
                    title: "Pongal Festival",
                    description: "Learn about Tamil Nadu's harvest festival",
                    difficulty: "Beginner",
                    timeEstimate: "15 mins",
                    progress: 0.0,
                    culturalNote: "Pongal is dedicated to the Sun God and marks the end of winter solstice",
                    gradient: LinearGradient(
                        colors: [Color.orange.opacity(0.8), Color.red.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                
                EnhancedCulturalCard(
                    title: "Tamil Literature",
                    description: "Explore classical Sangam poetry and epics",
                    difficulty: "Intermediate",
                    timeEstimate: "20 mins", 
                    progress: 0.3,
                    culturalNote: "Tamil literature spans over 2000 years with works like Thirukkural",
                    gradient: LinearGradient(
                        colors: [Color.purple.opacity(0.8), Color.blue.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                
                EnhancedCulturalCard(
                    title: "Chola Dynasty",
                    description: "Discover the golden age of Tamil history",
                    difficulty: "Advanced",
                    timeEstimate: "25 mins",
                    progress: 0.0,
                    culturalNote: "The Cholas built magnificent temples and had a powerful navy",
                    gradient: LinearGradient(
                        colors: [Color.indigo.opacity(0.8), Color.cyan.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                
                EnhancedCulturalCard(
                    title: "Tamil Script Evolution",
                    description: "Journey through 2000+ years of script development",
                    difficulty: "Intermediate",
                    timeEstimate: "18 mins",
                    progress: 0.6,
                    culturalNote: "Tamil script evolved from ancient Brahmi script",
                    gradient: LinearGradient(
                        colors: [Color.teal.opacity(0.8), Color.green.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                
                EnhancedCulturalCard(
                    title: "Bharatanatyam Dance",
                    description: "Learn about Tamil's classical dance form",
                    difficulty: "Beginner",
                    timeEstimate: "12 mins",
                    progress: 1.0,
                    culturalNote: "Bharatanatyam originated in Tamil Nadu temples",
                    gradient: LinearGradient(
                        colors: [Color.pink.opacity(0.8), Color.purple.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            }
            .padding()
        }
    }
}

struct EnhancedCulturalCard: View {
    let title: String
    let description: String
    let difficulty: String
    let timeEstimate: String
    let progress: Double
    let culturalNote: String
    let gradient: LinearGradient
    
    var body: some View {
        ZStack {
            // Glow effect background
            RoundedRectangle(cornerRadius: 20)
                .fill(gradient)
                .blur(radius: 8)
                .opacity(0.3)
                .scaleEffect(1.05)
            
            VStack(alignment: .leading, spacing: 12) {
                // Top accent bar
                Rectangle()
                    .fill(gradient)
                    .frame(height: 4)
                    .frame(maxWidth: .infinity)
                
                VStack(alignment: .leading, spacing: 12) {
                    // Header with badges
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Image(systemName: "book")
                                    .foregroundColor(.blue)
                                Text("Cultural")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                            
                            HStack {
                                ForEach(0..<3) { index in
                                    Image(systemName: "star.fill")
                                        .font(.caption2)
                                        .foregroundColor(index < difficultyStars ? .yellow : .gray.opacity(0.3))
                                }
                            }
                        }
                        
                        Spacer()
                        
                        HStack {
                            Image(systemName: "clock")
                                .font(.caption)
                            Text(timeEstimate)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Title and description
                    Text(title)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    // Cultural note
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Cultural Note:")
                            .font(.caption)
                            .fontWeight(.semibold)
                        Text(culturalNote)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    
                    // Progress section
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Progress")
                                .font(.caption)
                                .fontWeight(.medium)
                            Spacer()
                            Text("\(Int(progress * 100))%")
                                .font(.caption)
                        }
                        
                        ProgressView(value: progress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .scaleEffect(y: 0.5)
                    }
                    
                    // Completion badge
                    if progress == 1.0 {
                        HStack {
                            Spacer()
                            HStack {
                                Image(systemName: "checkmark")
                                    .font(.caption)
                                Text("Completed")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.green)
                            .cornerRadius(12)
                            Spacer()
                        }
                    }
                }
                .padding()
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    private var difficultyStars: Int {
        switch difficulty.lowercased() {
        case "beginner": return 1
        case "intermediate": return 2
        case "advanced": return 3
        default: return 1
        }
    }
}

// Extension for system background color
extension Color {
    static let systemBackground = Color(UIColor.systemBackground)
}

#Preview {
    EnhancedLessonsView()
} 