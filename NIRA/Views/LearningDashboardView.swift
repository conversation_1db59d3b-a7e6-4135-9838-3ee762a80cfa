import SwiftUI

struct LearningDashboardView: View {
    @StateObject private var fsrsService = FSRSService()
    @StateObject private var assessmentService = MicroAssessmentService()
    @State private var dueReviewsCount: Int = 0
    @State private var currentStreak: Int = 0
    @State private var shouldShowAssessment: Bool = false
    @State private var recentProgress: [DashboardSkillProgress] = []
    @State private var isLoading = true
    
    let userId: String
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    if isLoading {
                        loadingView
                    } else {
                        // Header with user progress
                        headerSection
                        
                        // Quick actions section
                        quickActionsSection
                        
                        // Assessment prompt (if needed)
                        if shouldShowAssessment {
                            assessmentPromptSection
                        }
                        
                        // Progress overview
                        progressOverviewSection
                        
                        // Learning insights
                        insightsSection
                    }
                }
                .padding()
            }
            .navigationTitle("Learning Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await loadDashboardData()
            }
            .task {
                await loadDashboardData()
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading your dashboard...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: 300)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Welcome back!")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Ready to continue your Tamil journey?")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Streak indicator
                if currentStreak > 0 {
                    VStack(spacing: 4) {
                        HStack {
                            Image(systemName: "flame.fill")
                                .foregroundColor(.orange)
                            Text("\(currentStreak)")
                                .font(.title2)
                                .fontWeight(.bold)
                        }
                        
                        Text("day streak")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
        }
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.bold)
            
            HStack(spacing: 12) {
                // Review cards action
                ActionCard(
                    title: "Review Cards",
                    subtitle: dueReviewsCount > 0 ? "\(dueReviewsCount) due" : "All up to date",
                    icon: "brain.head.profile",
                    color: .blue,
                    badge: dueReviewsCount > 0 ? "\(dueReviewsCount)" : nil,
                    action: {
                        // Navigate to FSRS Review View
                    }
                )
                
                // Continue lessons action
                ActionCard(
                    title: "Continue Lessons",
                    subtitle: "Pick up where you left off",
                    icon: "book.fill",
                    color: .green,
                    action: {
                        // Navigate to lessons
                    }
                )
            }
            
            HStack(spacing: 12) {
                // Practice speaking
                ActionCard(
                    title: "Practice Speaking",
                    subtitle: "Improve pronunciation",
                    icon: "mic.fill",
                    color: .red,
                    action: {
                        // Navigate to speaking practice
                    }
                )
                
                // View analytics
                ActionCard(
                    title: "View Analytics",
                    subtitle: "Track your progress",
                    icon: "chart.bar.fill",
                    color: .purple,
                    action: {
                        // Navigate to analytics
                    }
                )
            }
        }
    }
    
    // MARK: - Assessment Prompt Section
    private var assessmentPromptSection: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Quick Assessment Available")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Text("You've completed several lessons. Take a quick assessment to track your progress!")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                Image(systemName: "checkmark.seal.fill")
                    .font(.title)
                    .foregroundColor(.green)
            }
            
            HStack(spacing: 12) {
                Button("Take Assessment") {
                    // Navigate to micro assessment
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.green)
                )
                
                Button("Remind Later") {
                    shouldShowAssessment = false
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.green.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Progress Overview Section
    private var progressOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Your Progress")
                .font(.headline)
                .fontWeight(.bold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(recentProgress, id: \.skill) { progress in
                    AssessmentProgressCard(
                        skillCategory: progress.skill,
                        currentProgress: progress.progress,
                        recentScores: progress.recentScores
                    )
                }
            }
        }
    }
    
    // MARK: - Insights Section
    private var insightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Learning Insights")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                DashboardInsightCard(
                    icon: "target",
                    title: "Focus Recommendation",
                    description: "Grammar exercises will help strengthen your foundation",
                    actionTitle: "Practice Grammar",
                    color: .orange
                )
                
                DashboardInsightCard(
                    icon: "clock",
                    title: "Optimal Study Time",
                    description: "You perform best during morning sessions (9-11 AM)",
                    actionTitle: "Set Reminder",
                    color: .blue
                )
                
                DashboardInsightCard(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Progress Trend",
                    description: "Great job! You've improved by 15% this week",
                    actionTitle: "View Details",
                    color: .green
                )
            }
        }
    }
    
    // MARK: - Data Loading
    private func loadDashboardData() async {
        isLoading = true
        defer { isLoading = false }
        
        await loadDueReviews()
        await loadCurrentStreak()
        await checkAssessmentStatus()
        await loadRecentProgress()
    }
    
    private func loadDueReviews() async {
        // Mock implementation - replace with actual service call
        // TODO: Implement getDueReviews method in FSRSService
        dueReviewsCount = Int.random(in: 0...15)
    }
    
    private func loadCurrentStreak() async {
        // Mock implementation - replace with actual service call
        // TODO: Implement getCurrentStreak method in FSRSService
        currentStreak = Int.random(in: 0...30)
    }
    
    private func checkAssessmentStatus() async {
        // Mock implementation - replace with actual service call
        // TODO: Implement shouldTriggerAssessment method in MicroAssessmentService
        shouldShowAssessment = Bool.random()
    }
    
    private func loadRecentProgress() async {
        // Mock data for progress
        recentProgress = [
            DashboardSkillProgress(
                skill: .vocabulary,
                progress: 0.75,
                recentScores: [0.8, 0.7, 0.85, 0.9]
            ),
            DashboardSkillProgress(
                skill: .grammar,
                progress: 0.45,
                recentScores: [0.4, 0.5, 0.4, 0.5]
            ),
            DashboardSkillProgress(
                skill: .culture,
                progress: 0.85,
                recentScores: [0.8, 0.9, 0.85]
            ),
            DashboardSkillProgress(
                skill: .listening,
                progress: 0.65,
                recentScores: [0.6, 0.7, 0.65]
            )
        ]
    }
}

// MARK: - Supporting Views
struct ActionCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let badge: String?
    let action: () -> Void
    
    init(title: String, subtitle: String, icon: String, color: Color, badge: String? = nil, action: @escaping () -> Void) {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.color = color
        self.badge = badge
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                ZStack {
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(color)
                    
                    if let badge = badge {
                        VStack {
                            HStack {
                                Spacer()
                                Text(badge)
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(4)
                                    .background(
                                        Circle()
                                            .fill(Color.red)
                                    )
                                    .offset(x: 8, y: -8)
                            }
                            Spacer()
                        }
                    }
                }
                .frame(height: 30)
                
                VStack(spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.secondarySystemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct DashboardInsightCard: View {
    let icon: String
    let title: String
    let description: String
    let actionTitle: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(actionTitle) {
                // Action implementation
            }
            .font(.caption)
            .foregroundColor(color)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

// MARK: - Supporting Types
struct DashboardSkillProgress {
    let skill: SkillCategory
    let progress: Double
    let recentScores: [Double]
}

#Preview {
    LearningDashboardView(userId: "test-user-id")
} 