#!/usr/bin/env python3
"""
🔍 Quick Lesson Quality Verification
===================================
Verify the quality of regenerated Tamil A1 Lesson 1
"""

import requests
import json

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAzNDg2MCwiZXhwIjoyMDYzNjEwODYwfQ.XWPv7bqjwCZ0aYmAIxnWtICe8CU9r1eIw7mESbOns44"

def get_lesson_content():
    """Get lesson 1 content from database"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json'
    }
    
    # Get lesson 1 ID
    lesson_url = f"{SUPABASE_URL}/rest/v1/lessons"
    lesson_params = {
        'select': 'id,title_english',
        'language_code': 'eq.ta',
        'cefr_level': 'eq.A1',
        'lesson_number': 'eq.1'
    }
    
    lesson_response = requests.get(lesson_url, headers=headers, params=lesson_params)
    lessons = lesson_response.json()
    
    if not lessons:
        print("❌ Lesson 1 not found")
        return None
    
    lesson_id = lessons[0]['id']
    lesson_title = lessons[0]['title_english']
    
    print(f"📚 Checking Lesson 1: {lesson_title}")
    print(f"   Lesson ID: {lesson_id}")
    
    # Get all content types
    content = {}
    
    # Vocabulary
    vocab_url = f"{SUPABASE_URL}/rest/v1/lesson_vocabulary"
    vocab_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'word_order'}
    vocab_response = requests.get(vocab_url, headers=headers, params=vocab_params)
    content['vocabulary'] = vocab_response.json()
    
    # Conversations
    conv_url = f"{SUPABASE_URL}/rest/v1/lesson_conversations"
    conv_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'conversation_order'}
    conv_response = requests.get(conv_url, headers=headers, params=conv_params)
    content['conversations'] = conv_response.json()
    
    # Grammar
    grammar_url = f"{SUPABASE_URL}/rest/v1/lesson_grammar"
    grammar_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'grammar_order'}
    grammar_response = requests.get(grammar_url, headers=headers, params=grammar_params)
    content['grammar'] = grammar_response.json()
    
    # Exercises
    exercise_url = f"{SUPABASE_URL}/rest/v1/lesson_exercises"
    exercise_params = {'lesson_id': f'eq.{lesson_id}', 'order': 'exercise_order'}
    exercise_response = requests.get(exercise_url, headers=headers, params=exercise_params)
    content['exercises'] = exercise_response.json()
    
    return content

def analyze_content_quality(content):
    """Analyze the quality of the content"""
    print("\n🔍 CONTENT QUALITY ANALYSIS")
    print("=" * 50)
    
    # Vocabulary Analysis
    vocabulary = content.get('vocabulary', [])
    print(f"\n📝 VOCABULARY ({len(vocabulary)} items):")
    
    if vocabulary:
        # Show first 5 vocabulary items
        for i, item in enumerate(vocabulary[:5]):
            english = item.get('word_english', 'N/A')
            tamil = item.get('word_target_language', 'N/A')
            romanized = item.get('word_romanized', 'N/A')
            example_eng = item.get('example_sentence_english', 'N/A')
            example_tam = item.get('example_sentence_target_language', 'N/A')
            
            print(f"   {i+1}. {english} = {tamil} ({romanized})")
            print(f"      Example: {example_eng}")
            print(f"      Tamil: {example_tam}")
            print()
        
        if len(vocabulary) > 5:
            print(f"   ... and {len(vocabulary) - 5} more vocabulary items")
    
    # Conversations Analysis
    conversations = content.get('conversations', [])
    print(f"\n💬 CONVERSATIONS ({len(conversations)} items):")
    
    if conversations:
        # Show first 2 conversations
        for i, conv in enumerate(conversations[:2]):
            title_eng = conv.get('title_english', 'N/A')
            title_tam = conv.get('title_target_language', 'N/A')
            context = conv.get('context_description', 'N/A')
            dialogue = conv.get('conversation_content', [])
            
            print(f"   {i+1}. {title_eng} / {title_tam}")
            print(f"      Context: {context}")
            print(f"      Dialogue lines: {len(dialogue) if isinstance(dialogue, list) else 'N/A'}")
            
            # Show first dialogue line if available
            if isinstance(dialogue, list) and dialogue:
                first_line = dialogue[0]
                if isinstance(first_line, dict):
                    speaker = first_line.get('speaker', 'Unknown')
                    english = first_line.get('english', 'N/A')
                    tamil = first_line.get('tamil', 'N/A')
                    print(f"      {speaker}: {english}")
                    print(f"      {speaker}: {tamil}")
            print()
    
    # Grammar Analysis
    grammar = content.get('grammar', [])
    print(f"\n📖 GRAMMAR ({len(grammar)} items):")
    
    if grammar:
        for i, gram in enumerate(grammar[:3]):
            concept_eng = gram.get('concept_name_english', 'N/A')
            concept_tam = gram.get('concept_name_target_language', 'N/A')
            explanation = gram.get('explanation_english', 'N/A')
            
            print(f"   {i+1}. {concept_eng} / {concept_tam}")
            print(f"      {explanation}")
            print()
    
    # Exercises Analysis
    exercises = content.get('exercises', [])
    print(f"\n🎯 EXERCISES ({len(exercises)} items):")
    
    if exercises:
        exercise_types = {}
        for ex in exercises:
            ex_type = ex.get('exercise_type', 'unknown')
            exercise_types[ex_type] = exercise_types.get(ex_type, 0) + 1
        
        print("   Exercise type distribution:")
        for ex_type, count in exercise_types.items():
            print(f"      {ex_type}: {count}")
        
        # Show first exercise
        if exercises:
            first_ex = exercises[0]
            ex_type = first_ex.get('exercise_type', 'N/A')
            instructions = first_ex.get('instructions_english', 'N/A')
            print(f"\n   Example ({ex_type}):")
            print(f"      Instructions: {instructions}")
    
    # Quality Summary
    print(f"\n📊 QUALITY SUMMARY:")
    print(f"   ✅ Vocabulary: {len(vocabulary)}/25 items")
    print(f"   ✅ Conversations: {len(conversations)}/10 items")
    print(f"   ✅ Grammar: {len(grammar)}/5 items")
    print(f"   ✅ Exercises: {len(exercises)}/10 items")
    
    total_expected = 25 + 10 + 5 + 10
    total_actual = len(vocabulary) + len(conversations) + len(grammar) + len(exercises)
    completeness = (total_actual / total_expected) * 100
    
    print(f"   📈 Completeness: {completeness:.1f}% ({total_actual}/{total_expected})")
    
    # Check for lesson relevance (greetings theme)
    greeting_keywords = ['hello', 'hi', 'name', 'meet', 'greeting', 'introduction', 'வணக்கம்', 'பெயர்']
    relevant_vocab = 0
    
    for item in vocabulary:
        english = item.get('word_english', '').lower()
        tamil = item.get('word_target_language', '')
        if any(keyword in english or keyword in tamil for keyword in greeting_keywords):
            relevant_vocab += 1
    
    relevance = (relevant_vocab / len(vocabulary)) * 100 if vocabulary else 0
    print(f"   🎯 Relevance: {relevance:.1f}% ({relevant_vocab}/{len(vocabulary)} greeting-related vocabulary)")
    
    if completeness >= 90 and relevance >= 20:
        print(f"\n🎉 QUALITY ASSESSMENT: EXCELLENT!")
    elif completeness >= 80 and relevance >= 15:
        print(f"\n✅ QUALITY ASSESSMENT: GOOD!")
    elif completeness >= 70:
        print(f"\n⚠️  QUALITY ASSESSMENT: ACCEPTABLE")
    else:
        print(f"\n❌ QUALITY ASSESSMENT: NEEDS IMPROVEMENT")

def main():
    print("🔍 TAMIL A1 LESSON 1 QUALITY VERIFICATION")
    print("=" * 60)
    
    content = get_lesson_content()
    if content:
        analyze_content_quality(content)
    else:
        print("❌ Could not retrieve lesson content")

if __name__ == "__main__":
    main()
