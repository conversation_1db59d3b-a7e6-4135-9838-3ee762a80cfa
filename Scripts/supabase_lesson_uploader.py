#!/usr/bin/env python3
"""
🚀 SUPABASE LESSON CONTENT UPLOADER
==================================
Efficiently uploads Tamil A1 lesson content to Supabase database

FEATURES:
- Multi-process parallel upload
- Comprehensive content upload (vocabulary, conversations, grammar, exercises)
- Error handling and retry logic
- Progress tracking
"""

import json
import os
import requests
import time
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAzNDg2MCwiZXhwIjoyMDYzNjEwODYwfQ.XWPv7bqjwCZ0aYmAIxnWtICe8CU9r1eIw7mESbOns44"

# Global variables for tracking
upload_stats = {
    'total_lessons': 0,
    'completed_lessons': 0,
    'total_vocabulary': 0,
    'total_conversations': 0,
    'total_grammar': 0,
    'total_exercises': 0,
    'errors': []
}
stats_lock = threading.Lock()

def get_lesson_id_mapping():
    """Get mapping of lesson numbers to lesson IDs from Supabase"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json'
    }

    url = f"{SUPABASE_URL}/rest/v1/lessons"
    params = {
        'select': 'id,lesson_number',
        'language_code': 'eq.ta',
        'cefr_level': 'eq.A1'
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        lessons = response.json()

        lesson_mapping = {}
        for lesson in lessons:
            lesson_mapping[lesson['lesson_number']] = lesson['id']

        print(f"✅ Retrieved {len(lesson_mapping)} lesson ID mappings")
        return lesson_mapping

    except Exception as e:
        print(f"❌ Error getting lesson mappings: {e}")
        return {}

def upload_vocabulary_batch(lesson_id: str, vocabulary_items: List[Dict], lesson_num: int):
    """Upload vocabulary items for a lesson"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

    # First, delete existing vocabulary for this lesson
    delete_url = f"{SUPABASE_URL}/rest/v1/lesson_vocabulary"
    delete_params = {'lesson_id': f'eq.{lesson_id}'}

    try:
        delete_response = requests.delete(delete_url, headers=headers, params=delete_params)
        print(f"🗑️  Lesson {lesson_num}: Cleared existing vocabulary")
    except Exception as e:
        print(f"⚠️  Lesson {lesson_num}: Warning clearing vocabulary: {e}")

    # Prepare vocabulary data for upload
    vocab_data = []
    for item in vocabulary_items:
        vocab_data.append({
            'lesson_id': lesson_id,
            'word_order': item['word_order'],
            'word_english': item['word_english'],
            'word_target_language': item['word_target_language'],
            'word_romanized': item['word_romanized'],
            'word_ipa': item['word_ipa'],
            'part_of_speech': item['part_of_speech'],
            'difficulty_level': item['difficulty_level'],
            'example_sentence_english': item['example_sentence_english'],
            'example_sentence_target_language': item['example_sentence_target_language'],
            'example_sentence_romanized': item['example_sentence_romanized'],
            'cultural_notes': item.get('cultural_notes', '')
        })

    # Upload vocabulary in batches
    batch_size = 10
    total_uploaded = 0

    for i in range(0, len(vocab_data), batch_size):
        batch = vocab_data[i:i + batch_size]

        try:
            url = f"{SUPABASE_URL}/rest/v1/lesson_vocabulary"
            response = requests.post(url, headers=headers, json=batch)
            response.raise_for_status()
            total_uploaded += len(batch)
            print(f"📚 Lesson {lesson_num}: Uploaded {len(batch)} vocabulary items ({total_uploaded}/{len(vocab_data)})")

        except Exception as e:
            error_msg = f"Lesson {lesson_num} vocabulary batch {i//batch_size + 1}: {e}"
            with stats_lock:
                upload_stats['errors'].append(error_msg)
            print(f"❌ {error_msg}")
            return False

    with stats_lock:
        upload_stats['total_vocabulary'] += total_uploaded

    return True

def upload_conversations_batch(lesson_id: str, conversations: List[Dict], lesson_num: int):
    """Upload conversation content for a lesson"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

    # Delete existing conversations
    delete_url = f"{SUPABASE_URL}/rest/v1/lesson_conversations"
    delete_params = {'lesson_id': f'eq.{lesson_id}'}

    try:
        delete_response = requests.delete(delete_url, headers=headers, params=delete_params)
        print(f"🗑️  Lesson {lesson_num}: Cleared existing conversations")
    except Exception as e:
        print(f"⚠️  Lesson {lesson_num}: Warning clearing conversations: {e}")

    # Prepare conversation data to match actual schema
    conv_data = []
    for conv in conversations:
        conv_data.append({
            'lesson_id': lesson_id,
            'conversation_order': conv['conversation_order'],
            'title_english': conv['title_english'],
            'title_target_language': conv['title_target_language'],
            'context_description': conv['context_description'],
            'participants': conv['participants'],  # Already a dict/list, no need to json.dumps
            'conversation_type': 'dialogue',
            'formality_level': conv['formality_level'],
            'cultural_context': conv.get('cultural_notes', ''),
            'conversation_content': conv['dialogue_lines'],  # Already a dict/list
            'has_audio': False,
            'key_phrases': [],
            'grammar_points_covered': [],
            'vocabulary_featured': []
        })

    try:
        url = f"{SUPABASE_URL}/rest/v1/lesson_conversations"
        response = requests.post(url, headers=headers, json=conv_data)
        response.raise_for_status()

        with stats_lock:
            upload_stats['total_conversations'] += len(conv_data)

        print(f"💬 Lesson {lesson_num}: Uploaded {len(conv_data)} conversations")
        return True

    except Exception as e:
        error_msg = f"Lesson {lesson_num} conversations: {e}"
        with stats_lock:
            upload_stats['errors'].append(error_msg)
        print(f"❌ {error_msg}")
        return False

def upload_grammar_batch(lesson_id: str, grammar_points: List[Dict], lesson_num: int):
    """Upload grammar content for a lesson"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

    # Delete existing grammar
    delete_url = f"{SUPABASE_URL}/rest/v1/lesson_grammar"
    delete_params = {'lesson_id': f'eq.{lesson_id}'}

    try:
        requests.delete(delete_url, headers=headers, params=delete_params)
        print(f"🗑️  Lesson {lesson_num}: Cleared existing grammar")
    except Exception as e:
        print(f"⚠️  Lesson {lesson_num}: Warning clearing grammar: {e}")

    # Prepare grammar data to match actual schema
    grammar_data = []
    for gram in grammar_points:
        grammar_data.append({
            'lesson_id': lesson_id,
            'grammar_order': gram['grammar_order'],
            'concept_name_english': gram['concept_name_english'],
            'concept_name_target_language': gram.get('concept_name_tamil', ''),
            'explanation_english': gram['explanation_english'],
            'explanation_target_language': gram.get('explanation_tamil', ''),
            'explanation_romanized': gram.get('explanation_romanized', ''),
            'examples': gram.get('examples', []),  # Already a list, no need to json.dumps
            'practice_sentences': [],
            'difficulty_level': gram['difficulty_level'],
            'related_concepts': [],
            'cultural_usage_notes': gram.get('cultural_usage_notes', ''),
            'has_audio': False
        })

    try:
        url = f"{SUPABASE_URL}/rest/v1/lesson_grammar"
        response = requests.post(url, headers=headers, json=grammar_data)
        response.raise_for_status()

        with stats_lock:
            upload_stats['total_grammar'] += len(grammar_data)

        print(f"📖 Lesson {lesson_num}: Uploaded {len(grammar_data)} grammar points")
        return True

    except Exception as e:
        error_msg = f"Lesson {lesson_num} grammar: {e}"
        with stats_lock:
            upload_stats['errors'].append(error_msg)
        print(f"❌ {error_msg}")
        return False

def upload_exercises_batch(lesson_id: str, exercises: List[Dict], lesson_num: int):
    """Upload exercise content for a lesson"""
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

    # Delete existing exercises
    delete_url = f"{SUPABASE_URL}/rest/v1/lesson_exercises"
    delete_params = {'lesson_id': f'eq.{lesson_id}'}

    try:
        requests.delete(delete_url, headers=headers, params=delete_params)
        print(f"🗑️  Lesson {lesson_num}: Cleared existing exercises")
    except Exception as e:
        print(f"⚠️  Lesson {lesson_num}: Warning clearing exercises: {e}")

    # Prepare exercise data to match actual schema
    exercise_data = []
    for ex in exercises:
        exercise_data.append({
            'lesson_id': lesson_id,
            'exercise_order': ex['exercise_order'],
            'exercise_type': ex['exercise_type'],
            'title_english': f"Exercise {ex['exercise_order']}: {ex['exercise_type'].title()}",
            'title_target_language': f"பயிற்சி {ex['exercise_order']}",
            'instructions_english': ex['instructions_english'],
            'instructions_target_language': ex.get('instructions_tamil', ''),
            'exercise_content': ex['question_content'],  # Already a dict, no need to json.dumps
            'difficulty_level': ex['difficulty_level'],
            'targets_vocabulary': [],
            'targets_grammar': [],
            'targets_conversation': [],
            'max_score': 10,
            'passing_score': 7,
            'feedback_correct': {"message": "Correct! Well done!"},
            'feedback_incorrect': {"message": "Try again!"},
            'has_audio': False
        })

    try:
        url = f"{SUPABASE_URL}/rest/v1/lesson_exercises"
        response = requests.post(url, headers=headers, json=exercise_data)
        response.raise_for_status()

        with stats_lock:
            upload_stats['total_exercises'] += len(exercise_data)

        print(f"🎯 Lesson {lesson_num}: Uploaded {len(exercise_data)} exercises")
        return True

    except Exception as e:
        error_msg = f"Lesson {lesson_num} exercises: {e}"
        with stats_lock:
            upload_stats['errors'].append(error_msg)
        print(f"❌ {error_msg}")
        return False

def process_single_lesson(lesson_file: str, lesson_mapping: Dict[int, str]):
    """Process and upload a single lesson file"""
    try:
        # Extract lesson number from filename
        lesson_num = int(lesson_file.split('_')[-1].split('.')[0])

        if lesson_num not in lesson_mapping:
            print(f"❌ Lesson {lesson_num}: No lesson ID found in database")
            return False

        lesson_id = lesson_mapping[lesson_num]

        # Load lesson data
        file_path = f"Content/Tamil/A1/{lesson_file}"
        with open(file_path, 'r', encoding='utf-8') as f:
            lesson_data = json.load(f)

        print(f"\n🚀 Processing Lesson {lesson_num}: {lesson_data['lesson_metadata']['title_english']}")

        # Upload each content type
        success_count = 0

        # 1. Upload Vocabulary
        if upload_vocabulary_batch(lesson_id, lesson_data['vocabulary_section']['words'], lesson_num):
            success_count += 1

        # 2. Upload Conversations
        if upload_conversations_batch(lesson_id, lesson_data['conversation_section']['conversations'], lesson_num):
            success_count += 1

        # 3. Upload Grammar
        if upload_grammar_batch(lesson_id, lesson_data['grammar_section']['grammar_points'], lesson_num):
            success_count += 1

        # 4. Upload Exercises
        if upload_exercises_batch(lesson_id, lesson_data['exercise_section']['exercises'], lesson_num):
            success_count += 1

        # Update completion stats
        with stats_lock:
            upload_stats['completed_lessons'] += 1

        if success_count == 4:
            print(f"✅ Lesson {lesson_num}: All content uploaded successfully!")
            return True
        else:
            print(f"⚠️  Lesson {lesson_num}: Partial upload ({success_count}/4 content types)")
            return False

    except Exception as e:
        error_msg = f"Lesson processing error for {lesson_file}: {e}"
        with stats_lock:
            upload_stats['errors'].append(error_msg)
        print(f"❌ {error_msg}")
        return False

def main():
    """Main execution function with parallel processing"""
    print("🚀 SUPABASE LESSON CONTENT UPLOADER")
    print("=" * 60)
    print("📋 Target: Tamil A1 Lessons to Supabase Database")
    print("🎯 Content: Vocabulary + Conversations + Grammar + Exercises")
    print("")

    start_time = time.time()

    # Get lesson ID mappings
    print("📡 Connecting to Supabase...")
    lesson_mapping = get_lesson_id_mapping()

    if not lesson_mapping:
        print("❌ Failed to get lesson mappings. Exiting.")
        return

    # Find all lesson files
    lesson_files = []
    content_dir = "Content/Tamil/A1"

    if os.path.exists(content_dir):
        for file in os.listdir(content_dir):
            if file.startswith('complete_lesson_') and file.endswith('.json'):
                lesson_files.append(file)

    if not lesson_files:
        print(f"❌ No lesson files found in {content_dir}")
        return

    lesson_files.sort()  # Process in order
    upload_stats['total_lessons'] = len(lesson_files)

    print(f"📚 Found {len(lesson_files)} lesson files to upload")
    print(f"🔄 Starting parallel upload with {min(4, len(lesson_files))} workers...")
    print("")

    # Process lessons in parallel
    successful_uploads = 0
    failed_uploads = 0

    with ThreadPoolExecutor(max_workers=4) as executor:
        # Submit all tasks
        future_to_file = {
            executor.submit(process_single_lesson, lesson_file, lesson_mapping): lesson_file
            for lesson_file in lesson_files
        }

        # Process completed tasks
        for future in as_completed(future_to_file):
            lesson_file = future_to_file[future]
            try:
                success = future.result()
                if success:
                    successful_uploads += 1
                else:
                    failed_uploads += 1

                # Progress update
                completed = successful_uploads + failed_uploads
                print(f"📊 Progress: {completed}/{len(lesson_files)} lessons processed")

            except Exception as e:
                failed_uploads += 1
                error_msg = f"Future execution error for {lesson_file}: {e}"
                with stats_lock:
                    upload_stats['errors'].append(error_msg)
                print(f"❌ {error_msg}")

    # Final statistics
    end_time = time.time()
    duration = end_time - start_time

    print("\n" + "=" * 60)
    print("🎉 UPLOAD COMPLETE!")
    print("=" * 60)
    print(f"⏱️  Total Time: {duration:.2f} seconds")
    print(f"📚 Lessons Processed: {upload_stats['completed_lessons']}/{upload_stats['total_lessons']}")
    print(f"✅ Successful: {successful_uploads}")
    print(f"❌ Failed: {failed_uploads}")
    print("")
    print("📊 CONTENT UPLOADED:")
    print(f"   📝 Vocabulary Items: {upload_stats['total_vocabulary']}")
    print(f"   💬 Conversations: {upload_stats['total_conversations']}")
    print(f"   📖 Grammar Points: {upload_stats['total_grammar']}")
    print(f"   🎯 Exercises: {upload_stats['total_exercises']}")

    if upload_stats['errors']:
        print(f"\n⚠️  ERRORS ({len(upload_stats['errors'])}):")
        for error in upload_stats['errors'][:10]:  # Show first 10 errors
            print(f"   • {error}")
        if len(upload_stats['errors']) > 10:
            print(f"   ... and {len(upload_stats['errors']) - 10} more errors")

    print("\n🚀 Next Steps:")
    print("   1. Verify content in Supabase dashboard")
    print("   2. Test lesson loading in NIRA app")
    print("   3. Generate audio files for uploaded content")
    print("   4. Run quality assurance checks")

if __name__ == "__main__":
    main()